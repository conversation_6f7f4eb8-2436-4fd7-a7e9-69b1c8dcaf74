/**
 * Hotjar Debug Utility
 * Helps debug Hotjar initialization and user tracking issues
 */

import { HOTJAR_CONFIG } from './hotjar';

// Enable debug mode for development
export const HOTJAR_DEBUG = process.env.NODE_ENV === 'development';

/**
 * Debug version of initializeHotjar that logs status
 */
export function initializeHotjarWithDebug(): void {
  if (HOTJAR_DEBUG) {
    console.log('[Hotjar Debug] Configuration:', {
      enabled: HOTJAR_CONFIG.enabled,
      environment: process.env.NODE_ENV,
      hjid: HOTJAR_CONFIG.hjid,
      hjsv: HOTJAR_CONFIG.hjsv
    });
  }

  // Force enable for debugging (remove in production)
  const forceEnable = HOTJAR_DEBUG && window.location.search.includes('hotjar=true');
  
  if (!HOTJAR_CONFIG.enabled && !forceEnable) {
    if (HOTJAR_DEBUG) {
      console.log('[Hotjar Debug] Hotjar is disabled. Running in:', process.env.NODE_ENV);
      console.log('[Hotjar Debug] To force enable in dev, add ?hotjar=true to URL');
    }
    return;
  }

  if (typeof window === 'undefined') {
    if (HOTJAR_DEBUG) {
      console.log('[Hotjar Debug] Window is undefined, skipping initialization');
    }
    return;
  }

  if (window.hj) {
    if (HOTJAR_DEBUG) {
      console.log('[Hotjar Debug] Hotjar already initialized');
    }
    return;
  }

  try {
    // Initialize Hotjar
    (function(h,o,t,j,a,r){
      h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
      h._hjSettings={hjid:HOTJAR_CONFIG.hjid,hjsv:HOTJAR_CONFIG.hjsv};
      a=o.getElementsByTagName('head')[0];
      r=o.createElement('script');r.async=1;
      r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
      a.appendChild(r);
      
      if (HOTJAR_DEBUG) {
        console.log('[Hotjar Debug] Script injected:', r.src);
      }
    })(window,document,HOTJAR_CONFIG.scriptUrl,'.js?sv=');
    
    // Add load listener for debugging
    if (HOTJAR_DEBUG) {
      setTimeout(() => {
        if (window.hj) {
          console.log('[Hotjar Debug] Hotjar loaded successfully');
        } else {
          console.error('[Hotjar Debug] Hotjar failed to load after 3 seconds');
        }
      }, 3000);
    }
  } catch (error) {
    if (HOTJAR_DEBUG) {
      console.error('[Hotjar Debug] Error initializing Hotjar:', error);
    }
  }
}

/**
 * Debug version of identifyHotjarUser that logs user data
 */
export function identifyHotjarUserWithDebug(userId: string, attributes?: Record<string, any>): void {
  if (HOTJAR_DEBUG) {
    console.log('[Hotjar Debug] Identifying user:', {
      userId,
      attributes,
      hjAvailable: !!window.hj,
      enabled: HOTJAR_CONFIG.enabled
    });
  }

  if (!HOTJAR_CONFIG.enabled || typeof window === 'undefined' || !window.hj) {
    if (HOTJAR_DEBUG) {
      console.warn('[Hotjar Debug] Cannot identify user - Hotjar not available');
    }
    return;
  }

  try {
    window.hj('identify', userId, attributes || {});
    if (HOTJAR_DEBUG) {
      console.log('[Hotjar Debug] User identified successfully');
    }
  } catch (error) {
    if (HOTJAR_DEBUG) {
      console.error('[Hotjar Debug] Error identifying user:', error);
    }
  }
}

/**
 * Check Hotjar status
 */
export function checkHotjarStatus(): void {
  console.group('[Hotjar Status Check]');
  console.log('Environment:', process.env.NODE_ENV);
  console.log('Config enabled:', HOTJAR_CONFIG.enabled);
  console.log('Window available:', typeof window !== 'undefined');
  console.log('Hotjar loaded:', typeof window !== 'undefined' && !!window.hj);
  console.log('Hotjar settings:', typeof window !== 'undefined' && window._hjSettings);
  
  if (typeof window !== 'undefined' && window.hj) {
    console.log('✅ Hotjar is loaded and ready');
  } else {
    console.log('❌ Hotjar is not loaded');
    console.log('To enable in development, add ?hotjar=true to URL');
  }
  console.groupEnd();
}

// Export utility for window
if (typeof window !== 'undefined') {
  (window as any).checkHotjarStatus = checkHotjarStatus;
}