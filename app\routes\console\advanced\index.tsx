import { ClientOnly } from 'remix-utils/client-only';
import ConnectUI from 'sections/advanced/connect-ui';
import { Paper } from '@mui/material';

export default function Index() {
    return (
        <ClientOnly fallback={null}>
            {() => (
                <Paper 
                    elevation={2}
                    sx={{ 
                        p: 3,
                        borderRadius: '8px',
                        backgroundColor: (theme) => theme.palette.background.paper
                    }}
                >
                    <ConnectUI />
                </Paper>
            )}
        </ClientOnly>
    );
}
