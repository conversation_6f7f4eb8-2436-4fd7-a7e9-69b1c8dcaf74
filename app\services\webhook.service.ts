import fetchInstance from 'utils/api/fetchinstance';
import { API_ENDPOINTS } from 'utils/api/api-endpoints';
import { 
  Webhook, 
  CreateWebhookPayload, 
  UpdateWebhookPayload
} from 'types/webhook';

export const webhookClient = {
  // GET /api/v1/webhooks - List all webhooks
  getWebhooks: async (): Promise<any> => {
    const response = await fetchInstance.get<any>(API_ENDPOINTS.WEBHOOKS);
    return response.data;
  },
  searchWebhooks: async (payload: any): Promise<any> => {
    const response = await fetchInstance.post<any>(`${API_ENDPOINTS.WEBHOOKS}/search`, payload);
    return response.data;
  },

  // GET /api/v1/webhooks/{id} - Get specific webhook
  getWebhook: async (webhookId: string): Promise<any> => {
    const response = await fetchInstance.get<any>(`${API_ENDPOINTS.WEBHOOKS}/${webhookId}`);
    return response.data;
  },

  // POST /api/v1/webhooks - Create new webhook
  createWebhook: async (payload: any): Promise<any> => {
    const response = await fetchInstance.post<any>(API_ENDPOINTS.WEBHOOKS, payload);
    return response.data;
  },

  // PATCH /api/v1/webhooks/{id} - Partial update webhook
  updateWebhook: async (webhookId: string, payload: Partial<any>): Promise<any> => {
    const response = await fetchInstance.patch<any>(`${API_ENDPOINTS.WEBHOOKS}/${webhookId}`, payload);
    return response.data;
  },

  // DELETE /api/v1/webhooks/{id} - Delete webhook
  deleteWebhook: async (webhookId: string): Promise<void> => {
    await fetchInstance.delete(`${API_ENDPOINTS.WEBHOOKS}/${webhookId}`);
  },

  // Utility method to validate webhook endpoint
  validateWebhookEndpoint: async (url: string): Promise<{ ok: boolean; status: number; statusText: string; message?: string }> => {
    // Basic client-side payload validation before calling API
    if (typeof url !== 'string' || url.trim().length === 0) {
      throw new Error('Endpoint URL is required');
    }
    let parsed: URL;
    try {
      parsed = new URL(url);
    } catch {
      throw new Error('Please provide a valid URL');
    }
    if (!['http:', 'https:'].includes(parsed.protocol)) {
      throw new Error('Invalid protocol. Only HTTP/HTTPS are supported');
    }

    const payload = { url: url.trim() };
    const response = await fetchInstance.post(`${API_ENDPOINTS.WEBHOOKS}/validate`, payload);
    const { data, status, statusText } = response as any;

    // Normalize various possible server response shapes to a single contract
    const okFromBody = (
      data?.ok === true ||
      data?.valid === true ||
      data?.reachable === true ||
      data?.result === 'OK' ||
      data?.status === 'OK' ||
      data?.success === true
    );
    const ok = okFromBody || (typeof status === 'number' && status >= 200 && status < 300);

    return {
      ok,
      status: typeof status === 'number' ? status : 200,
      statusText: statusText || data?.statusText || data?.message || '',
      message: data?.message
    };
  }
};

// Legacy support for existing code using old organization-based endpoints
export const legacyWebhookClient = {
  getWebhooks: async (organizationId: string): Promise<any[]> => {
    const { data } = await fetchInstance.get(`/organizations/${organizationId}/configurations`);
    return data;
  },

  createWebhook: async (organizationId: string, payload: any): Promise<any> => {
    const { data } = await fetchInstance.post(
      `/organizations/${organizationId}/configurations`,
      payload
    );
    return data;
  },

  updateWebhook: async (
    organizationId: string, 
    webhookId: string, 
    payload: any
  ): Promise<any> => {
    const { data } = await fetchInstance.patch(
      `/organizations/${organizationId}/configurations/${webhookId}`,
      payload
    );
    return data;
  },

  deleteWebhook: async (organizationId: string, webhookId: string): Promise<void> => {
    await fetchInstance.delete(`/organizations/${organizationId}/configurations/${webhookId}`);
  }
};