import { CheckOutlined, DownOutlined, SettingOutlined } from "@ant-design/icons";
import { <PERSON>ton, <PERSON><PERSON>r, Menu, MenuItem, Skeleton, Stack, Typography } from "@mui/material";
import { useInfiniteQuery, useMutation } from "@tanstack/react-query";
import Dot from "components/@extended/Dot";
import { EnvironmentSwitchRequestState } from "constants/environment";
import useEnvironment from "hooks/api/useEnvironment";
import { useEffect, useId, useState } from "react";
import { useNavigate } from "@remix-run/react";
import Environment from "types/environment";

import SimpleBarScroll from "components/third-party/SimpleBar";
import { AxiosResponse } from "axios";
import { getIsEnvDefault, getIsEnvInUse } from "sections/environments/utils";
import { toast } from "sonner";
import { parseError } from "lib/utils";
import { formatTimeAgo } from "utils/date";
import { ResponseModel } from "types/common";
import { getEnvironmentId, setEnvironmentId } from "utils/environment-id";
import useUserDetails from "store/user";

const isLoading = false;

const EnvironmentSwitcher = () => {

   const environmentId = getEnvironmentId();

   const { environments } = useUserDetails();

   const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
   const [selectedEnv, setSelectedEnv] = useState<Environment.Root | null>(null);

   const prefix = useId();
   const navigate = useNavigate()

   const open = Boolean(anchorEl);
   const buttonId = prefix + "positioned-button";
   const menuId = prefix + "positioned-menu";


   const handleClick = (event: React.MouseEvent<HTMLElement>) => {
      setAnchorEl(event.currentTarget);
   };

   const handleClose = () => {
      setAnchorEl(null);
   };

   const onSelect = async (environment: Environment.Root) => {
      try {
         if (!environment?.id) {
            toast.error('Invalid environment selected');
            return;
         }
         handleClose();
         setEnvironmentId(environment.id);
         window.location.reload();
      } catch (error) {
         console.error('Error selecting environment:', error);
         toast.error('Failed to switch environment');
      }
   }

   const onManageEnvironments = () => {
      navigate('/console/environments')
      handleClose();
   }

   useEffect(() => {
      if (environmentId && environments?.length > 0) {
         const env = environments.find(getIsEnvInUse);
         setSelectedEnv(env || environments[0] || null);
      } else if (environments?.length > 0 && !selectedEnv) {
         // Fallback to first environment if none selected
         setSelectedEnv(environments[0]);
      }
   }, [environmentId, environments?.length])

   return (
      <>
         {isLoading ? (
            <Skeleton sx={{ width: 200 }} />
         ) : (
            <Button
               id={buttonId}
               aria-controls={open ? buttonId : undefined}
               aria-haspopup="true"
               aria-expanded={open ? 'true' : undefined}
               onClick={handleClick}
               color="secondary"
               startIcon={<Dot sx={{ bgcolor: selectedEnv?.colorPicker as string || 'primary.main' }} />}
               endIcon={<DownOutlined />}
               variant="outlined"
               sx={({ palette }) => {
                  return (
                     {
                        borderColor: palette.grey[300],
                     }
                  )
               }}
            >
               {selectedEnv?.name || (environments?.length > 0 ? 'Select Environment' : 'No Environments')}
            </Button>
         )}

         <Menu
            id={menuId}
            aria-labelledby={buttonId}
            anchorEl={anchorEl}
            open={open}
            onClose={handleClose}
            anchorOrigin={{
               vertical: 'bottom',
               horizontal: 'left',
            }}
            transformOrigin={{
               vertical: 'top',
               horizontal: 'left',
            }}
            sx={{
               mt: 1,
            }}
         >
            <div className="px-4 py-3 ">
               <Typography>Switch Environment</Typography>
               <Typography variant="body2" color={'grey'} >Select your working environment</Typography>
            </div>
            <Divider />
            <SimpleBarScroll
               sx={{
                  maxHeight: 230,
                  '& .simplebar-content':
                     { display: 'flex', flexDirection: 'column', },
               }}
            >
               {environments?.length > 0 ? environments.map((environment, index) => {
                  const selected = environment?.id === selectedEnv?.id;
                  if (!environment) return null;
                  return (
                     <MenuItem
                        key={index}
                        onClick={() => onSelect(environment)}
                        sx={{
                           gap: 2,
                           display: 'flex',
                           justifyContent: 'space-between'
                        }}
                        selected={selected}
                     >
                        <Stack direction={'row'} alignItems={'center'} gap={2}>
                           <Dot sx={{ bgcolor: environment.colorPicker as string || 'primary.main' }} />
                           <Stack>
                              <Typography>{environment.name || 'Unnamed Environment'}</Typography>
                              <Typography 
                                 variant="caption" 
                                 sx={{ 
                                    color: 'text.secondary',
                                    fontSize: '0.75rem',
                                    lineHeight: 1.2
                                 }}
                              >
                                 {environment?.changeLog?.createdDateTime 
                                    ? `Created ${formatTimeAgo(environment.changeLog.createdDateTime)}`
                                    : 'Recently created'
                                 }
                              </Typography>
                           </Stack>
                        </Stack>
                        {selected && <CheckOutlined />}
                     </MenuItem>
                  )
               }) : (
                  <MenuItem disabled>
                     <Typography variant="body2" color="text.secondary">
                        No environments available
                     </Typography>
                  </MenuItem>
               )}
            </SimpleBarScroll>

            <Divider />
            <MenuItem
               onClick={onManageEnvironments}
               className="flex items-center space-x-3 px-4 py-2 text-left transition-colors cursor-pointer group"
            >
               <SettingOutlined className="w-4 h-4" />
               <Typography className="text-sm font-medium ">
                  Manage Environments
               </Typography>
            </MenuItem>
         </Menu>
      </>
   )
}

export default EnvironmentSwitcher;