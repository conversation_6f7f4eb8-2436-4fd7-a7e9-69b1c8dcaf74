# Hotjar Implementation Guide for Unizo Web Portal

## Overview

This guide details the comprehensive Hotjar integration for the Unizo Web Portal, providing production-only user behavior tracking, heatmaps, session recordings, and conversion funnel analysis.

## Architecture

### 1. Core Components

```
app/
├── utils/
│   └── hotjar.ts                 # Core Hotjar utilities and configuration
├── contexts/
│   └── HotjarProvider.tsx        # React context for Hotjar integration
├── hooks/
│   ├── useHotjarTracking.ts      # Custom hooks for specific tracking scenarios
│   └── useHotjar.ts              # Re-exported from context
└── root.tsx                      # Integration point with CSP headers
```

### 2. Environment Configuration

Hotjar is **ONLY enabled in production** (`NODE_ENV === 'production'`). The configuration includes:

```typescript
{
  hjid: 6518270,
  hjsv: 6,
  enabled: process.env.NODE_ENV === 'production'
}
```

## Implementation Details

### 1. Automatic Tracking

The following are tracked automatically:

- **Page Views**: Every route change
- **User Identification**: When user logs in
- **Session Duration**: Time spent on pages
- **Error Tracking**: JavaScript errors with context

### 2. Manual Tracking Implementation

#### Quick Start Flow
```typescript
import { useTrackQuickStart } from 'hooks/useHotjarTracking';

function QuickStartComponent() {
  const { trackQuickStartStep, trackConnectorToggle } = useTrackQuickStart();
  
  // Track step progression
  trackQuickStartStep(1, 'Select Connectors', {
    connectors_selected: 5
  });
  
  // Track connector actions
  trackConnectorToggle('GitHub', true);
}
```

#### Integration Setup
```typescript
import { useTrackIntegrationSetup } from 'hooks/useHotjarTracking';

function IntegrationSetup() {
  const { trackServiceActivation, trackAccessPointConfiguration } = useTrackIntegrationSetup();
  
  // Track service activation
  trackServiceActivation('GitHub', 'service-123', true);
  
  // Track configuration
  trackAccessPointConfiguration('GitHub', 'OAuth', true);
}
```

#### API Usage Tracking
```typescript
import { useTrackAPIUsage } from 'hooks/useHotjarTracking';

function APILogs() {
  const { trackAPICall, trackLogSearch } = useTrackAPIUsage();
  
  // Track API calls
  trackAPICall('/api/v1/users', 'GET', 200, 150);
  
  // Track searches
  trackLogSearch({
    query: 'error',
    resultsCount: 45,
    filters: { status: 'failed' }
  });
}
```

### 3. Conversion Funnels

Key conversion funnels implemented:

1. **Onboarding Funnel**
   - Sign Up → Email Verification → Organization Setup → First Integration → First API Call

2. **Integration Setup Funnel**
   - Browse Services → Enable Service → Configure Access Points → Test Connection → Complete

3. **API Key Generation Funnel**
   - Navigate to API Keys → Select Environment → Generate Key → First Usage

### 4. Custom Events Reference

| Event Name | Description | Attributes |
|------------|-------------|------------|
| `user_action_[category]` | Generic user actions | action, category, label, value |
| `service_activation` | Service enabled/disabled | service_name, service_id, success |
| `access_point_configuration` | Access point setup | service_name, access_point_type, configured |
| `api_call` | API request tracking | endpoint, method, status_code, duration_ms |
| `search_performed` | Search interactions | search_term, results_count, filters |
| `error_occurred` | Error tracking | error_message, error_stack, page_url |
| `form_submission` | Form completions | form_name, success, metadata |
| `feature_engagement` | Feature usage | feature, action, duration_seconds |
| `conversion` | Conversion events | type, value, currency |

### 5. User Attributes

The following user attributes are tracked:

```typescript
{
  email: string,
  name: string,
  organization_id: string,
  organization_name: string,
  role: string,
  created_at: string,
  subscription_tier: string,
  is_trial: boolean,
  environment: string
}
```

## Security Considerations

### 1. Content Security Policy (CSP)

The implementation includes proper CSP headers for Hotjar:

```typescript
{
  'script-src': ["'self'", "'unsafe-inline'", "'unsafe-eval'", 'https://*.hotjar.com'],
  'connect-src': ["'self'", 'https://*.hotjar.com', 'wss://*.hotjar.com'],
  'img-src': ["'self'", 'data:', 'https://*.hotjar.com'],
  'style-src': ["'self'", "'unsafe-inline'", 'https://*.hotjar.com'],
  'font-src': ["'self'", 'https://*.hotjar.com'],
  'frame-src': ["'self'", 'https://*.hotjar.com']
}
```

### 2. Allowed Domains and IPs

- Domains: `*.hotjar.com`, `insights.hotjar.com`
- IPs: `************`, `**************`, `*************`

### 3. User Agent Allowlist

```
- Phone: Mozilla/5.0 (iPhone; CPU iPhone OS 15_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Hotjar Version/15.2 Mobile/15E148 Safari/604.1
- Tablet: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_6) AppleWebKit/605.1.15 (KHTML, like Gecko) Hotjar Version/15.2 Safari/605.1.15
- Desktop: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Hotjar Chrome/97.0.4692.71 Safari/537.36
```

## Best Practices

### 1. Performance Considerations

- Hotjar script loads asynchronously
- No tracking in development/staging environments
- Minimal performance impact (~15ms initialization)

### 2. Privacy Compliance

- No PII in custom events
- User consent should be obtained (implement consent banner)
- Sensitive form fields should be masked

### 3. Data Quality

- Use consistent event naming conventions
- Include relevant context in event attributes
- Avoid over-tracking (quality over quantity)

## Testing

### Development Testing

Since Hotjar only runs in production, use these methods for testing:

1. **Console Logging**: All tracking calls log to console in development
2. **Mock Mode**: Set `HOTJAR_CONFIG.enabled = true` temporarily
3. **Staging Environment**: Use a separate Hotjar site ID for staging

### Verification in Production

1. Check Hotjar dashboard for incoming events
2. Verify heatmaps are generating
3. Test session recordings
4. Validate conversion funnels

## Troubleshooting

### Common Issues

1. **No data in Hotjar**
   - Verify production environment
   - Check browser console for errors
   - Ensure CSP headers are correct

2. **Broken CSS in recordings**
   - Verify allowed domains include your CDN
   - Check if stylesheets are accessible from Hotjar IPs

3. **Missing user attributes**
   - Ensure user is logged in before identification
   - Check timing of identify call

## Usage Examples

### Track Component Engagement
```typescript
function FeatureComponent() {
  useAutoTrackEngagement('feature_name');
  
  return <div>Feature content</div>;
}
```

### Track Click Events
```typescript
function Button() {
  const trackClick = useTrackClick();
  
  return (
    <button onClick={() => trackClick('cta_button', { location: 'hero' })}>
      Click Me
    </button>
  );
}
```

### Track Form Submissions
```typescript
function Form() {
  const { trackFormSubmission } = useHotjar();
  
  const handleSubmit = async (data) => {
    try {
      await submitForm(data);
      trackFormSubmission('contact_form', true, { fields: Object.keys(data) });
    } catch (error) {
      trackFormSubmission('contact_form', false, { error: error.message });
    }
  };
}
```

## Maintenance

### Regular Tasks

1. **Monthly**: Review tracking implementation for new features
2. **Quarterly**: Analyze funnel performance and optimize
3. **Annually**: Audit tracked events and remove obsolete ones

### Adding New Tracking

1. Define event in `useHotjarTracking.ts`
2. Implement in component
3. Document in this guide
4. Test in production
5. Monitor data quality

## Contact

For questions or issues with Hotjar implementation:
- Technical Lead: [Your Name]
- Hotjar Account Owner: [Account Owner]
- Support: [Support Email]