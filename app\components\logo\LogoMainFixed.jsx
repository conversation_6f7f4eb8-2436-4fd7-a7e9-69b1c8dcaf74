import { Link } from '@remix-run/react';
import { ButtonBase } from '@mui/material';
import { useConfig } from 'contexts/ConfigContext';

const LogoMain = ({ reverse, isIcon, sx, to }) => {
  const { mode } = useConfig();
  const isWhite = reverse || mode === 'dark';
  
  // Direct paths to logo files
  const logoPath = isIcon 
    ? `/images/brand/unz_min_brand_${isWhite ? 'dark' : 'light'}.svg`
    : `/images/brand/unz_brand_${isWhite ? 'dark' : 'light'}.svg`;

  return (
    <ButtonBase
      disableRipple
      component={Link}
      to={!to ? '/dashboard' : to}
      sx={sx}
    >
      <img
        src={logoPath}
        alt="Unizo Logo"
        style={{
          height: isIcon ? '32px' : '40px',
          width: 'auto'
        }}
        onError={(e) => {
          console.error('Failed to load logo:', logoPath);
          // Fallback to text if image fails
          e.target.style.display = 'none';
          e.target.insertAdjacentHTML('afterend', '<span style="font-size: 24px; font-weight: bold; color: currentColor;">UNIZO</span>');
        }}
      />
    </ButtonBase>
  );
};

export default LogoMain;