/**
 * Custom hooks for Hotjar tracking in specific components and flows
 */

import { useCallback, useEffect } from 'react';
import { useHotjar, useTrackClick, useTrackView, useTrackConversion } from 'contexts/HotjarProvider';

/**
 * Track Quick Start flow
 */
export const useTrackQuickStart = () => {
  const { trackFunnelStep, trackUserAction } = useHotjar();
  const trackClick = useTrackClick();

  const trackQuickStartStep = useCallback((step: number, stepName: string, metadata?: Record<string, any>) => {
    trackFunnelStep('quick_start', stepName, step, {
      total_steps: 3,
      ...metadata
    });
  }, [trackFunnelStep]);

  const trackConnectorToggle = useCallback((connectorName: string, enabled: boolean) => {
    trackUserAction('toggle_connector', 'quick_start', connectorName, enabled ? 1 : 0);
  }, [trackUserAction]);

  const trackConnectorConfiguration = useCallback((connectorName: string, success: boolean) => {
    trackUserAction('configure_connector', 'quick_start', connectorName, success ? 1 : 0);
  }, [trackUserAction]);

  return {
    trackQuickStartStep,
    trackConnectorToggle,
    trackConnectorConfiguration,
    trackClick
  };
};

/**
 * Track Integration setup flow
 */
export const useTrackIntegrationSetup = () => {
  const { trackEvent, trackFunnelStep, trackFormSubmission } = useHotjar();

  const trackServiceActivation = useCallback((serviceName: string, serviceId: string, success: boolean) => {
    trackEvent('service_activation', {
      service_name: serviceName,
      service_id: serviceId,
      success,
      timestamp: new Date().toISOString()
    });
  }, [trackEvent]);

  const trackAccessPointConfiguration = useCallback((
    serviceName: string, 
    accessPointType: string, 
    configured: boolean
  ) => {
    trackEvent('access_point_configuration', {
      service_name: serviceName,
      access_point_type: accessPointType,
      configured,
      timestamp: new Date().toISOString()
    });
  }, [trackEvent]);

  const trackWebhookSetup = useCallback((serviceName: string, webhookUrl: string, events: string[]) => {
    trackEvent('webhook_configuration', {
      service_name: serviceName,
      webhook_url: webhookUrl,
      events_count: events.length,
      events: events.join(','),
      timestamp: new Date().toISOString()
    });
  }, [trackEvent]);

  return {
    trackServiceActivation,
    trackAccessPointConfiguration,
    trackWebhookSetup,
    trackFormSubmission
  };
};

/**
 * Track API usage and logs
 */
export const useTrackAPIUsage = () => {
  const { trackEvent, trackSearch, trackUserAction } = useHotjar();

  const trackAPICall = useCallback((endpoint: string, method: string, statusCode: number, duration: number) => {
    trackEvent('api_call', {
      endpoint,
      method,
      status_code: statusCode,
      duration_ms: duration,
      success: statusCode >= 200 && statusCode < 300,
      timestamp: new Date().toISOString()
    });
  }, [trackEvent]);

  const trackLogSearch = useCallback((searchParams: {
    query?: string;
    filters?: Record<string, any>;
    dateRange?: { start: string; end: string };
    resultsCount: number;
  }) => {
    trackSearch(searchParams.query || '', searchParams.resultsCount, {
      ...searchParams.filters,
      date_range: searchParams.dateRange
    });
  }, [trackSearch]);

  const trackLogExport = useCallback((format: string, recordCount: number) => {
    trackUserAction('export_logs', 'api_usage', format, recordCount);
  }, [trackUserAction]);

  return {
    trackAPICall,
    trackLogSearch,
    trackLogExport
  };
};

/**
 * Track user authentication and session
 */
export const useTrackAuthentication = () => {
  const { trackEvent, trackUserAction } = useHotjar();

  const trackLogin = useCallback((method: string, success: boolean, errorReason?: string) => {
    trackEvent('user_login', {
      method,
      success,
      error_reason: errorReason,
      timestamp: new Date().toISOString()
    });
  }, [trackEvent]);

  const trackLogout = useCallback((reason: string) => {
    trackEvent('user_logout', {
      reason,
      timestamp: new Date().toISOString()
    });
  }, [trackEvent]);

  const trackSessionTimeout = useCallback(() => {
    trackEvent('session_timeout', {
      timestamp: new Date().toISOString()
    });
  }, [trackEvent]);

  return {
    trackLogin,
    trackLogout,
    trackSessionTimeout
  };
};

/**
 * Track environment and configuration changes
 */
export const useTrackConfiguration = () => {
  const { trackEvent, trackUserAction } = useHotjar();

  const trackEnvironmentSwitch = useCallback((fromEnv: string, toEnv: string) => {
    trackEvent('environment_switch', {
      from_environment: fromEnv,
      to_environment: toEnv,
      timestamp: new Date().toISOString()
    });
  }, [trackEvent]);

  const trackAPIKeyGeneration = useCallback((keyType: string, environment: string) => {
    trackEvent('api_key_generated', {
      key_type: keyType,
      environment,
      timestamp: new Date().toISOString()
    });
  }, [trackEvent]);

  const trackSettingsChange = useCallback((settingName: string, oldValue: any, newValue: any) => {
    trackUserAction('change_setting', 'configuration', settingName);
    trackEvent('settings_changed', {
      setting_name: settingName,
      old_value: oldValue,
      new_value: newValue,
      timestamp: new Date().toISOString()
    });
  }, [trackEvent, trackUserAction]);

  return {
    trackEnvironmentSwitch,
    trackAPIKeyGeneration,
    trackSettingsChange
  };
};

/**
 * Track error and support interactions
 */
export const useTrackSupport = () => {
  const { trackEvent, trackError, trackUserFeedback } = useHotjar();

  const trackSupportTicket = useCallback((category: string, priority: string, description: string) => {
    trackEvent('support_ticket_created', {
      category,
      priority,
      has_description: !!description,
      timestamp: new Date().toISOString()
    });
  }, [trackEvent]);

  const trackDocumentationView = useCallback((docPage: string, timeSpent: number) => {
    trackEvent('documentation_viewed', {
      page: docPage,
      time_spent_seconds: timeSpent,
      timestamp: new Date().toISOString()
    });
  }, [trackEvent]);

  const trackFeedback = useCallback((type: string, rating: number, comment?: string) => {
    trackUserFeedback(type, rating, comment);
  }, [trackUserFeedback]);

  return {
    trackSupportTicket,
    trackDocumentationView,
    trackFeedback,
    trackError
  };
};

/**
 * Track billing and subscription events
 */
export const useTrackBilling = () => {
  const { trackEvent, trackConversion } = useHotjar();
  const conversion = useTrackConversion();

  const trackSubscriptionChange = useCallback((
    fromPlan: string, 
    toPlan: string, 
    changeType: 'upgrade' | 'downgrade',
    value?: number
  ) => {
    trackEvent('subscription_changed', {
      from_plan: fromPlan,
      to_plan: toPlan,
      change_type: changeType,
      value,
      timestamp: new Date().toISOString()
    });
    
    if (changeType === 'upgrade') {
      conversion('subscription_upgrade', value, { from_plan: fromPlan, to_plan: toPlan });
    }
  }, [trackEvent, conversion]);

  const trackPaymentMethod = useCallback((action: 'add' | 'update' | 'remove', methodType: string) => {
    trackEvent('payment_method_action', {
      action,
      method_type: methodType,
      timestamp: new Date().toISOString()
    });
  }, [trackEvent]);

  const trackInvoiceAction = useCallback((action: string, invoiceId: string, amount?: number) => {
    trackEvent('invoice_action', {
      action,
      invoice_id: invoiceId,
      amount,
      timestamp: new Date().toISOString()
    });
  }, [trackEvent]);

  return {
    trackSubscriptionChange,
    trackPaymentMethod,
    trackInvoiceAction
  };
};

/**
 * Auto-track component visibility and engagement
 */
export const useAutoTrackEngagement = (componentName: string, trackingEnabled: boolean = true) => {
  const trackView = useTrackView();
  const { trackEngagement } = useHotjar();
  
  useEffect(() => {
    if (!trackingEnabled) return;
    
    const startTime = Date.now();
    trackView(componentName);
    
    return () => {
      const duration = Math.round((Date.now() - startTime) / 1000);
      if (duration > 2) { // Only track if viewed for more than 2 seconds
        trackEngagement(componentName, 'viewed', duration);
      }
    };
  }, [componentName, trackingEnabled, trackView, trackEngagement]);
};