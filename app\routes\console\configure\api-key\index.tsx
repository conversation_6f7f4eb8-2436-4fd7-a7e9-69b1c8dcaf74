import { useState, useEffect } from 'react';
import { 
  Button, 
  <PERSON>ack, 
  Typography, 
  <PERSON>, 
  Grid, 
  Chip,
  useTheme,
  IconButton,
  Tooltip
} from "@mui/material";
import { alpha } from '@mui/material/styles';
import LoadingButton from '@mui/lab/LoadingButton';
import PageCard from "components/cards/PageCard";
import { RefreshCw, Link2, Key } from 'lucide-react';
import { useAccesskey } from "hooks/api/useAccesskey";
import KeyGenerationModal from './components/KeyGenerationModal';
import RegenerateDialog from './components/RegenerateDialog';
import ExpirationDropdown from './components/ExpirationDropdown';
import { toast } from 'sonner';

interface ApiKeyProps {
   showDescription?: boolean;
}

export default function ApiKey({ showDescription = false }: ApiKeyProps) {
   const theme = useTheme();
   const { 
      hasKey,
      AccessKey,
      generatedKey,
      generate, 
      isPending, 
      createdAt, 
      expiresAt,
      isExpired,
      daysRemaining,
      clearGeneratedKey
   } = useAccesskey();

   const [showGenerationModal, setShowGenerationModal] = useState(false);
   const [showRegenerateDialog, setShowRegenerateDialog] = useState(false);
   const [selectedExpiration, setSelectedExpiration] = useState(30);

   // Show generation modal when new key is generated
   useEffect(() => {
      if (generatedKey) {
         setShowGenerationModal(true);
      }
   }, [generatedKey]);

   const handleGenerate = async () => {
      if (hasKey) {
         setShowRegenerateDialog(true);
      } else {
         try {
            await generate(selectedExpiration);
         } catch (error) {
            toast.error('Failed to generate API key');
         }
      }
   };

   const handleRegenerate = async (expirationDays: number) => {
      try {
         await generate(expirationDays);
         setShowRegenerateDialog(false);
      } catch (error) {
         toast.error('Failed to regenerate API key');
      }
   };

   const handleCloseGenerationModal = () => {
      setShowGenerationModal(false);
      clearGeneratedKey();
   };

   const getStatusColor = () => {
      if (!hasKey) return 'default';
      if (isExpired) return 'error';
      if (daysRemaining !== null && daysRemaining <= 7) return 'warning';
      return 'success';
   };

   const getStatusText = () => {
      if (!hasKey) return 'No key generated';
      if (isExpired) return 'Expired';
      if (daysRemaining === null) return 'Active';
      if (daysRemaining === 0) return 'Expires today';
      if (daysRemaining === 1) return '1 day remaining';
      return `${daysRemaining} days remaining`;
   };

   return (
      <Stack spacing={3}>
         {showDescription && (
            <Typography variant='body2' color="text.secondary">
               Access keys are used to authenticate API requests to Unizo. Each key can be configured with specific permissions and used across different environments based on your application needs.
            </Typography>
         )}

         <PageCard sx={{ p: { xs: 2, sm: 3 } }}>
            <Stack spacing={2}>
               {/* Header Section */}
               <Box>
                  <Stack direction="row" alignItems="center" spacing={1} flexWrap="wrap">
                     <Typography variant="h6" fontWeight={600}>
                        Access key
                     </Typography>
                     <Link2 size={18} color={theme.palette.text.secondary} />
                     {hasKey && (
                        <Box sx={{ 
                           display: 'inline-flex', 
                           alignItems: 'center',
                           justifyContent: 'center',
                           ml: 1,
                           backgroundColor: theme.palette.mode === 'dark' 
                              ? alpha(theme.palette.background.paper, 0.4)
                              : theme.palette.grey[100],
                           borderRadius: 1,
                           px: 2,
                           py: 0.5,
                           border: `1px solid ${theme.palette.divider}`,
                           gap: 1
                        }}>
                           <Key 
                              size={16} 
                              color={theme.palette.text.secondary}
                           />
                           <Typography 
                              variant="body2" 
                              component="span"
                              sx={{ 
                                 fontFamily: 'Monaco, Consolas, "Courier New", monospace',
                                 color: theme.palette.text.secondary,
                                 fontSize: '0.875rem',
                                 letterSpacing: '0.1em',
                                 fontWeight: 300,
                                 opacity: 0.8
                              }}
                           >
                              {AccessKey}
                           </Typography>
                        </Box>
                     )}
                  </Stack>
                  <Stack direction="row" alignItems="center" spacing={1} flexWrap="wrap" sx={{ mt: 1.5 }}>
                     <Typography variant="body2" color="text.secondary">
                        Use this key to authenticate Unified API requests across any category.
                     </Typography>
                     <Typography
                        component="a"
                        href="https://docs.unizo.ai/docs/unizo-console/api-keys"
                        target="_blank"
                        rel="noopener noreferrer"
                        variant="body2"
                        sx={{
                           color: theme.palette.primary.main,
                           textDecoration: 'none',
                           whiteSpace: 'nowrap',
                           '&:hover': {
                              textDecoration: 'underline'
                           }
                        }}
                     >
                        Learn more
                     </Typography>
                  </Stack>
               </Box>

               {/* Key Details and Actions */}
               {hasKey ? (
                  <Stack 
                     direction={{ xs: 'column', sm: 'row' }} 
                     justifyContent="space-between"
                     alignItems={{ xs: 'stretch', sm: 'center' }}
                     spacing={2}
                  >
                     <Stack 
                        direction="row" 
                        spacing={{ xs: 2, sm: 3 }}
                        alignItems="center"
                        flexWrap="wrap"
                     >
                        <Box>
                           <Typography variant="caption" color="text.secondary" fontWeight={500} display="block">
                              Created
                           </Typography>
                           <Typography variant="body2">
                              {createdAt || '-'}
                           </Typography>
                        </Box>

                        <Box>
                           <Typography variant="caption" color="text.secondary" fontWeight={500} display="block">
                              Expires
                           </Typography>
                           <Typography variant="body2">
                              {expiresAt || '-'}
                           </Typography>
                        </Box>

                        <Box>
                           <Typography variant="caption" color="text.secondary" fontWeight={500} display="block">
                              Status
                           </Typography>
                           <Chip
                              label={getStatusText()}
                              size="small"
                              color={getStatusColor()}
                              sx={{
                                 height: 22,
                                 fontSize: '0.75rem',
                                 fontWeight: 500,
                                 '& .MuiChip-label': {
                                    px: 1
                                 }
                              }}
                           />
                        </Box>
                     </Stack>

                     <LoadingButton
                        variant="contained"
                        color="primary"
                        loading={isPending}
                        loadingPosition="start"
                        startIcon={<RefreshCw size={16} />}
                        onClick={handleGenerate}
                        sx={{
                           textTransform: 'none',
                           minWidth: { xs: '100%', sm: 140 },
                           whiteSpace: 'nowrap'
                        }}
                     >
                        Regenerate key
                     </LoadingButton>
                  </Stack>
               ) : (
                  <Stack 
                     direction="column"
                     spacing={2}
                     alignItems="flex-start"
                     sx={{ mt: 2 }}
                  >
                     <Box sx={{ width: 300 }}>
                        <ExpirationDropdown
                           value={selectedExpiration}
                           onChange={setSelectedExpiration}
                           showLabel={false}
                        />
                     </Box>
                     
                     <LoadingButton
                        variant="contained"
                        color="primary"
                        loading={isPending}
                        onClick={handleGenerate}
                        sx={{
                           textTransform: 'none',
                           px: 3,
                           py: 1
                        }}
                     >
                        Generate API key
                     </LoadingButton>
                  </Stack>
               )}
            </Stack>
         </PageCard>

         {/* Modals */}
         {generatedKey && (
            <KeyGenerationModal
               open={showGenerationModal}
               onClose={handleCloseGenerationModal}
               apiKey={generatedKey}
               expirationDays={selectedExpiration}
            />
         )}

         <RegenerateDialog
            open={showRegenerateDialog}
            onClose={() => setShowRegenerateDialog(false)}
            onConfirm={handleRegenerate}
            isLoading={isPending}
         />
      </Stack>
   );
}