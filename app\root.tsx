/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEffect, useLayoutEffect, useState } from "react";

import {
  Links,
  Meta,
  Outlet,
  Scripts,
  ScrollRestoration,
  json,
  useLoaderData,
  useNavigate,
  useNavigation,
} from "@remix-run/react";

import { QueryClient, QueryClientProvider, useInfiniteQuery } from "@tanstack/react-query";

import { AdapterMoment } from '@mui/x-date-pickers/AdapterMoment';
import { LocalizationProvider } from '@mui/x-date-pickers-pro';

import "./tailwind.css";
import "react-grid-layout/css/styles.css";
import "react-resizable/css/styles.css";

import { Toaster } from "components/@extended/toaster";

import LocalProvider from '../app/components/Locales';
import Loader from "components/Loader";

import ErrorBoundary from 'hoc/error-boundaries/global';
import { parseOrganizationDetailsFromServerRequest } from "utils/organizaton";
import { LoaderFunctionArgs } from "@remix-run/node";

import useOnboarding from "hooks/use-onboarding";
import { HotjarProvider } from "contexts/HotjarProvider";
import { getHotjarCSPDirectives, HOTJAR_CONFIG } from "utils/hotjar";

const isLocal = process.env.APP_ENV === 'local';

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { userId, orgId } = parseOrganizationDetailsFromServerRequest(request, isLocal);
  
  // Set CSP headers for Hotjar in production
  const headers: Record<string, string> = {};
  if (process.env.NODE_ENV === 'production') {
    const cspDirectives = getHotjarCSPDirectives();
    const cspHeader = Object.entries(cspDirectives)
      .map(([directive, values]) => `${directive} ${values.join(' ')}`)
      .join('; ');
    headers['Content-Security-Policy'] = cspHeader;
  }
  
  return json({
    userId,
    orgId,
    ENV: {
      API_PLATFORM_BASE_URL: process.env.API_PLATFORM_BASE_URL,
      API_POD_BASE_URL: process.env.API_POD_BASE_URL,
      KEYCLOAK_REALM_ID: process.env.KEYCLOAK_REALM_ID,
      KEYCLOAK_CLIENT_ID: process.env.KEYCLOAK_CLIENT_ID,
      KEYCLOAK_URI: process.env.KEYCLOAK_URI,
      WINDOW_ENV: process.env.NODE_ENV,
      HOTJAR_ENABLED: HOTJAR_CONFIG.enabled
    }
  }, { headers });
};

export function Layout({ children }: { children: React.ReactNode }) {
  const { userId, orgId, ENV } = useLoaderData<typeof loader>() ?? {};

  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            staleTime: 30 * 1000,
            retry: false,
            refetchOnWindowFocus: false,
          },
        },
      })
  );

  const { isReady } = useOnboarding({ orgId, userId });

  return (
    <html lang="en">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="" />
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet" />
        <Meta />
        <Links />
      </head>
      <body suppressHydrationWarning={true}>
        <ErrorBoundary>
          <QueryClientProvider client={queryClient}>
            <HotjarProvider>
              {isReady && children}
              <Toaster />
              <ScrollRestoration />
              <Scripts />
              <script
                dangerouslySetInnerHTML={{
                  __html: `
                window.authUserId = ${JSON.stringify(userId)}
                window.authUserOrgId = ${JSON.stringify(orgId)}
                window.ENV = ${JSON.stringify(ENV)}
              `,
                }}
              />
            </HotjarProvider>
          </QueryClientProvider>
        </ErrorBoundary>
      </body>
    </html>
  );
}


export default function App() {
  const navigation = useNavigation();

  return (
    <LocalProvider>
      <LocalizationProvider dateAdapter={AdapterMoment}>
        {navigation.state !== "idle" ? <Loader /> : null}
        <Outlet />
      </LocalizationProvider>
    </LocalProvider>
  );
}
