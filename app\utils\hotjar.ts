/**
 * Hotjar Integration Utilities
 * Production-only analytics and user behavior tracking
 */

// Hotjar configuration
export const HOTJAR_CONFIG = {
  hjid: 6518270,
  hjsv: 6,
  enabled: process.env.NODE_ENV === 'production',
  scriptUrl: 'https://static.hotjar.com/c/hotjar-',
  domains: ['*.hotjar.com', 'insights.hotjar.com'],
  allowedIPs: ['************', '**************', '*************'],
  userAgents: {
    phone: 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Hotjar Version/15.2 Mobile/15E148 Safari/604.1',
    tablet: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_6) AppleWebKit/605.1.15 (KHTML, like Gecko) Hotjar Version/15.2 Safari/605.1.15',
    desktop: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Hotjar Chrome/97.0.4692.71 Safari/537.36'
  }
};

// Type definitions for Hotjar
declare global {
  interface Window {
    hj: (command: string, ...args: any[]) => void;
    _hjSettings: {
      hjid: number;
      hjsv: number;
    };
  }
}

/**
 * Initialize Hotjar tracking
 * Only runs in production environment
 */
export function initializeHotjar(): void {
  if (!HOTJAR_CONFIG.enabled) {
    return;
  }

  if (typeof window === 'undefined') {
    return;
  }

  // Check if Hotjar is already initialized
  if (window.hj) {
    return;
  }

  try {
    // Initialize Hotjar
    (function(h,o,t,j,a,r){
      h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
      h._hjSettings={hjid:HOTJAR_CONFIG.hjid,hjsv:HOTJAR_CONFIG.hjsv};
      a=o.getElementsByTagName('head')[0];
      r=o.createElement('script');r.async=1;
      r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
      a.appendChild(r);
    })(window,document,HOTJAR_CONFIG.scriptUrl,'.js?sv=');
  } catch (error) {
    // Silently fail in production
  }
}

/**
 * Track custom events in Hotjar
 */
export function trackHotjarEvent(eventName: string, attributes?: Record<string, any>): void {
  if (!HOTJAR_CONFIG.enabled || typeof window === 'undefined' || !window.hj) {
    return;
  }

  try {
    window.hj('event', eventName);
    
    // Track additional attributes if provided
    if (attributes) {
      Object.entries(attributes).forEach(([key, value]) => {
        window.hj('attribute', key, value);
      });
    }
  } catch (error) {
    // Silently fail in production
  }
}

/**
 * Identify user in Hotjar
 */
export function identifyHotjarUser(userId: string, attributes?: Record<string, any>): void {
  if (!HOTJAR_CONFIG.enabled || typeof window === 'undefined' || !window.hj) {
    return;
  }

  try {
    window.hj('identify', userId, attributes || {});
  } catch (error) {
    // Silently fail in production
  }
}

/**
 * Track page view in Hotjar
 */
export function trackPageView(pageName: string, pageUrl?: string): void {
  if (!HOTJAR_CONFIG.enabled || typeof window === 'undefined' || !window.hj) {
    return;
  }

  try {
    // Hotjar automatically tracks page views, but we can trigger manual tracking
    window.hj('stateChange', pageUrl || window.location.pathname);
    
    // Track as custom event for additional context
    trackHotjarEvent('page_view', {
      page_name: pageName,
      page_url: pageUrl || window.location.pathname,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    // Silently fail in production
  }
}

/**
 * Track conversion funnel step
 */
export function trackFunnelStep(funnelName: string, stepName: string, stepNumber: number, metadata?: Record<string, any>): void {
  if (!HOTJAR_CONFIG.enabled || typeof window === 'undefined' || !window.hj) {
    return;
  }

  try {
    trackHotjarEvent(`funnel_${funnelName}_step_${stepNumber}`, {
      funnel_name: funnelName,
      step_name: stepName,
      step_number: stepNumber,
      ...metadata
    });
  } catch (error) {
    // Silently fail in production
  }
}

/**
 * Track user feedback
 */
export function trackUserFeedback(feedbackType: string, rating?: number, comment?: string): void {
  if (!HOTJAR_CONFIG.enabled || typeof window === 'undefined' || !window.hj) {
    return;
  }

  try {
    trackHotjarEvent('user_feedback', {
      feedback_type: feedbackType,
      rating,
      comment,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    // Silently fail in production
  }
}

/**
 * Generate Content Security Policy directives for Hotjar
 */
export function getHotjarCSPDirectives() {
  return {
    'script-src': [
      "'self'",
      "'unsafe-inline'",
      "'unsafe-eval'",
      'https://*.hotjar.com',
      'https://static.hotjar.com'
    ],
    'connect-src': [
      "'self'",
      'https://*.hotjar.com',
      'https://*.hotjar.io',
      'wss://*.hotjar.com'
    ],
    'img-src': [
      "'self'",
      'data:',
      'https://*.hotjar.com'
    ],
    'style-src': [
      "'self'",
      "'unsafe-inline'",
      'https://*.hotjar.com'
    ],
    'font-src': [
      "'self'",
      'https://*.hotjar.com'
    ],
    'frame-src': [
      "'self'",
      'https://*.hotjar.com'
    ]
  };
}