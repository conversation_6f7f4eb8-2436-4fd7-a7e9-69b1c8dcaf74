import { useMemo, useState } from "react";

import { ColumnFiltersState } from '@tanstack/react-table';
import { useGetServiceProfile } from "hooks/api/service-profile/useGetServiceProfile";
import useUserDetails from "store/user";

type UseGridFilterProps = {
   selectedProvider: string
   columnFilters: ColumnFiltersState
}

export default function useLogFilter(
   {
      selectedProvider,
      columnFilters
   }: UseGridFilterProps
) {

   const { subscriptions } = useUserDetails();

   const categoriesOptions = useMemo(() => {
      return subscriptions?.map((item) => (
         { label: item.name, value: item?.productKey }
      )) ?? []
   }, [subscriptions]);

   // Fix: Process the selectedProvider to handle array values and convert labels to values
   const processedType = useMemo(() => {
      if (!selectedProvider) return undefined;

      // If it's an array, extract the first value and convert label to value
      if (Array.isArray(selectedProvider)) {
         const firstValue = selectedProvider.length > 0 ? selectedProvider[0] : undefined;
         if (firstValue && categoriesOptions.length > 0) {
            const matched = categoriesOptions.find(opt => opt.label === firstValue);
            const result = matched?.value || firstValue;
            return result;
         }
         return firstValue;
      }

      // If it's a string, convert label to value
      if (categoriesOptions.length > 0) {
         const matched = categoriesOptions.find(opt => opt.label === selectedProvider);
         const result = matched?.value || selectedProvider;
         return result;
      }

      return selectedProvider;
   }, [selectedProvider, categoriesOptions]);

   // Always get ALL providers by passing a special value that won't filter
   const { serviceProfileClient } = useGetServiceProfile({
      searchOptions: {
         search: {
            limit: 200, // Increase limit to get more providers
            offset: 0,
            type: "GET_ALL_PROVIDERS" // Special value that we'll handle in the helper
         }
      }
   });

   console.log('useLogFilter - API Response Debug:', {
      serviceProfileClient,
      data: serviceProfileClient?.data,
      dataLength: serviceProfileClient?.data?.length,
      isLoading: serviceProfileClient?.isLoading,
      error: serviceProfileClient?.error
   });



   const providerOptions = useMemo(() => {
      const allProviders = serviceProfileClient?.data || [];

      // If no category is selected, show all providers
      if (!processedType) {
         console.log('useLogFilter - Showing ALL providers:', allProviders.length);
         return allProviders.map((item: any) => ({
            label: item.name,
            value: item?.id
         }));
      }

      // If category is selected, filter providers by type
      const filteredProviders = allProviders.filter((item: any) => item.type === processedType);

      console.log('useLogFilter - Filtering providers:', {
         processedType,
         totalProviders: allProviders.length,
         filteredProviders: filteredProviders.length,
         filteredProviderNames: filteredProviders.map((p: any) => p.name)
      });

      return filteredProviders.map((item: any) => ({
         label: item.name,
         value: item?.id
      }));
   }, [serviceProfileClient?.data, processedType]);

   return {
      providerOptions,
      categoriesOptions,
      onValuesChange: (values: any) => {
         const latest = [...columnFilters, ...values()]
      },
   }
}