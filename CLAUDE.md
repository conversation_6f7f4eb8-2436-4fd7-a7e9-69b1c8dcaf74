# Unizo Web Portal - Development Guidelines & Best Practices

## Page Layout Guidelines

### Standard Page Structure

Always use Grid-based layouts for page structure. Avoid nested Container components.

#### Page Header Pattern
```javascript
<Grid container spacing={3}>
  {/* Header */}
  <Grid item xs={12}>
    <Grid container justifyContent="space-between" alignItems="center">
      <Grid item>
        <Typography variant="h3" fontWeight={700} gutterBottom>
          Page Title
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Page description or subtitle
        </Typography>
      </Grid>
      <Grid item>
        {/* Action buttons */}
        <Button variant="contained">
          Action
        </Button>
      </Grid>
    </Grid>
  </Grid>

  {/* Main Content */}
  <Grid item xs={12}>
    <PageCard>
      {/* Content */}
    </PageCard>
  </Grid>
</Grid>
```

### Layout Best Practices

1. **Use Grid System** - Always use MUI Grid for page layouts
2. **Consistent Spacing** - Use `spacing={3}` as default for Grid containers
3. **No Nested Containers** - Avoid Container > Stack > Container patterns
4. **PageCard for Content** - Wrap main content sections in PageCard component
5. **Responsive by Default** - Use Grid breakpoints (xs, sm, md, lg, xl)
6. **Consistent Border Radius** - Use `borderRadius: '8px'` for page-level Paper components

### Page Wrapper Pattern

When wrapping entire page content in Paper components (e.g., webhooks, Connect UI), use consistent styling:

```javascript
<Paper 
  elevation={2}
  sx={{ 
    p: 3,
    borderRadius: '8px', // Always use 8px for page-level Paper components
    backgroundColor: (theme) => theme.palette.background.paper
  }}
>
  {/* Page content */}
</Paper>
```

### Search Input Pattern

For consistent search inputs across the application, use theme-based styling:

```javascript
<TextField
  placeholder="Search..."
  size="medium"
  value={searchQuery}
  onChange={(e) => setSearchQuery(e.target.value)}
  sx={{ 
    flex: 1,
    width: { xs: '100%', sm: 'auto' },
    '& .MuiOutlinedInput-root': {
      backgroundColor: theme.palette.mode === 'dark' 
        ? alpha(theme.palette.background.paper, 0.8)
        : theme.palette.grey[50],
      minHeight: 40,
      borderRadius: 0,
      '& fieldset': {
        borderColor: theme.palette.divider,
        borderRadius: 0,
      },
      '&:hover': {
        backgroundColor: theme.palette.mode === 'dark' 
          ? alpha(theme.palette.background.paper, 0.8)
          : theme.palette.grey[50],
        '& .MuiOutlinedInput-notchedOutline': {
          borderColor: theme.palette.mode === 'dark'
            ? alpha(theme.palette.divider, 1.5)
            : theme.palette.grey[400],
        }
      },
      '&.Mui-focused': {
        backgroundColor: theme.palette.mode === 'dark' 
          ? alpha(theme.palette.background.paper, 0.8)
          : theme.palette.grey[50],
        '& .MuiOutlinedInput-notchedOutline': {
          borderColor: theme.palette.primary.main,
          borderWidth: '1px',
        }
      }
    },
    '& .MuiInputBase-input': {
      padding: '8px 12px',
    }
  }}
  InputProps={{
    startAdornment: (
      <InputAdornment position="start">
        <Search size={20} style={{ color: theme.palette.text.secondary }} />
      </InputAdornment>
    ),
  }}
/>
```

### Common Page Patterns

#### List Page with Filters
```javascript
<Grid container spacing={3}>
  {/* Header */}
  <Grid item xs={12}>
    {/* Page title and actions */}
  </Grid>
  
  {/* Filters */}
  <Grid item xs={12}>
    <Paper>
      {/* Filter components */}
    </Paper>
  </Grid>
  
  {/* List/Table */}
  <Grid item xs={12}>
    <PageCard>
      {/* Table or list component */}
    </PageCard>
  </Grid>
</Grid>
```

#### Dashboard Page
```javascript
<Grid container spacing={3}>
  {/* Header */}
  <Grid item xs={12}>
    {/* Dashboard title */}
  </Grid>
  
  {/* Stats Cards */}
  <Grid item xs={12} sm={6} md={3}>
    <StatCard />
  </Grid>
  {/* Repeat for other stats */}
  
  {/* Charts/Widgets */}
  <Grid item xs={12} md={8}>
    <PageCard>
      {/* Chart component */}
    </PageCard>
  </Grid>
  <Grid item xs={12} md={4}>
    <PageCard>
      {/* Widget component */}
    </PageCard>
  </Grid>
</Grid>
```

## Theme-Based Styling Guidelines

### Core Principles

1. **NO HARDCODED COLORS** - All colors must come from the theme palette
2. **RESPONSIVE BY DEFAULT** - Use theme spacing and breakpoints
3. **DARK MODE SUPPORT** - All components must work in both light and dark modes
4. **CONSISTENCY** - Follow existing patterns in the codebase
5. **NO EMOJIS** - No Emojis in any implementation and docs

### Theme Color Usage

#### NEVER DO THIS:
```javascript
// Bad - Hardcoded colors
backgroundColor: '#ffffff'
color: '#21384f'
borderColor: 'rgba(255, 255, 255, 0.05)'
```

#### ALWAYS DO THIS:
```javascript
// Good - Theme-based colors
backgroundColor: theme.palette.background.paper
color: theme.palette.primary.main
borderColor: theme.palette.divider
```

### Available Theme Palette

```javascript
// Primary Colors
theme.palette.primary.lighter   // Lightest shade
theme.palette.primary.light
theme.palette.primary.main      // Main brand color
theme.palette.primary.dark
theme.palette.primary.darker    // Darkest shade
theme.palette.primary.contrastText

// Secondary Colors
theme.palette.secondary.lighter
theme.palette.secondary.light
theme.palette.secondary.main
theme.palette.secondary.dark
theme.palette.secondary.darker
theme.palette.secondary.contrastText

// Semantic Colors
theme.palette.error.*
theme.palette.warning.*
theme.palette.info.*
theme.palette.success.*

// Tertiary (Orange accent)
theme.palette.tertiary.*

// Background Colors
theme.palette.background.default  // Page background
theme.palette.background.paper    // Card/Paper background

// Text Colors
theme.palette.text.primary       // Main text
theme.palette.text.secondary     // Muted text
theme.palette.text.disabled      // Disabled text

// Action Colors
theme.palette.action.hover
theme.palette.action.selected
theme.palette.action.disabled
theme.palette.action.focus

// Utility Colors
theme.palette.divider           // Borders and dividers
theme.palette.grey[50-900]      // Grey scale
```

### Component Styling Patterns

#### 1. Using styled() from MUI
```javascript
import { styled } from '@mui/material/styles';

export const StyledComponent = styled(Box)(({ theme }) => ({
  backgroundColor: theme.palette.background.paper,
  color: theme.palette.text.primary,
  padding: theme.spacing(2),
  borderRadius: theme.shape.borderRadius,
  transition: theme.transitions.create(['box-shadow', 'transform']),
  '&:hover': {
    boxShadow: theme.shadows[4],
    transform: 'translateY(-2px)',
  }
}));
```

#### 2. Using sx prop
```javascript
<Box
  sx={{
    backgroundColor: (theme) => theme.palette.background.default,
    color: 'text.primary', // Shorthand notation
    p: 2, // theme.spacing(2)
    borderRadius: 1, // theme.shape.borderRadius
    '&:hover': {
      backgroundColor: 'action.hover',
    }
  }}
/>
```

#### 3. Conditional Styling Based on Mode
```javascript
backgroundColor: (theme) => 
  theme.palette.mode === 'dark' 
    ? theme.palette.grey[900] 
    : theme.palette.grey[50]
```

### Spacing Guidelines

Always use theme spacing units instead of hardcoded pixel values:

```javascript
// Bad
padding: '16px'
margin: '8px 16px'

// Good
padding: theme.spacing(2)     // 16px (assuming 8px base)
margin: theme.spacing(1, 2)   // 8px 16px
p: 2                         // Shorthand in sx prop
m: { xs: 1, sm: 2, md: 3 }  // Responsive spacing
```

### Shadow Usage

```javascript
// Use theme shadows
boxShadow: theme.shadows[1]  // Subtle shadow
boxShadow: theme.shadows[4]  // Medium shadow
boxShadow: theme.shadows[8]  // Strong shadow

// Custom shadows should use theme colors
boxShadow: `0 2px 8px ${alpha(theme.palette.common.black, 0.1)}`
```

### Border Patterns

```javascript
// Borders
border: `1px solid ${theme.palette.divider}`
borderColor: 'divider'  // Shorthand in sx
borderRadius: theme.shape.borderRadius
borderRadius: 1  // Shorthand = theme.spacing(1)

// Consistent Border Radius for Components
// Use 8px border radius for:
// - Page wrapper Paper components
// - Search input fields
// - Cards and containers
borderRadius: '8px'  // Standard for UI consistency
```

### Typography

```javascript
// Use theme typography
fontSize: theme.typography.body1.fontSize
fontWeight: theme.typography.fontWeightMedium

// Or use Typography component
<Typography variant="h4" color="text.primary">
  Title
</Typography>
```

## Page Layout Guidelines

### Consistent Page Structure

All pages should follow a consistent Grid-based layout pattern for better maintainability and responsive behavior.

#### NEVER DO THIS:
```javascript
// Bad - Nested containers with inconsistent spacing
<Container maxWidth="lg" sx={{ py: 4 }}>
  <Stack spacing={2}>
    <Container>
      {/* Content */}
    </Container>
  </Stack>
</Container>
```

#### ALWAYS DO THIS:
```javascript
// Good - Clean Grid-based layout
<Grid container spacing={3}>
  <Grid item xs={12}>
    {/* Page header */}
  </Grid>
  <Grid item xs={12}>
    {/* Main content */}
  </Grid>
</Grid>
```

### Standard Page Header Pattern

Use this pattern for consistent page headers:

```javascript
<Grid container spacing={3}>
  {/* Header Section */}
  <Grid item xs={12}>
    <Grid container justifyContent="space-between" alignItems="center">
      <Grid item>
        <Typography variant="h3" fontWeight={700} gutterBottom>
          Page Title
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Page description or subtitle goes here
        </Typography>
      </Grid>
      <Grid item>
        {/* Action buttons */}
        <Button variant="contained">
          Action
        </Button>
      </Grid>
    </Grid>
  </Grid>
  
  {/* Main Content */}
  <Grid item xs={12}>
    <PageCard>
      {/* Page content */}
    </PageCard>
  </Grid>
</Grid>
```

### Layout Best Practices

1. **Use Grid System** - Prefer MUI Grid over nested Containers
2. **Consistent Spacing** - Use spacing={3} as the default
3. **No Nested Containers** - Avoid Container inside Container
4. **Responsive by Default** - Use Grid breakpoints (xs, sm, md, lg, xl)
5. **Page Wrapping** - Main content should NOT use Container wrapper

### Common Page Patterns

#### List Page with Filters
```javascript
<Grid container spacing={3}>
  <Grid item xs={12}>
    {/* Page header */}
  </Grid>
  <Grid item xs={12} md={3}>
    {/* Filters sidebar */}
  </Grid>
  <Grid item xs={12} md={9}>
    {/* Main content list */}
  </Grid>
</Grid>
```

#### Dashboard Layout
```javascript
<Grid container spacing={3}>
  <Grid item xs={12}>
    {/* Page header */}
  </Grid>
  <Grid item xs={12} sm={6} md={4}>
    {/* Metric card 1 */}
  </Grid>
  <Grid item xs={12} sm={6} md={4}>
    {/* Metric card 2 */}
  </Grid>
  <Grid item xs={12} sm={12} md={4}>
    {/* Metric card 3 */}
  </Grid>
  <Grid item xs={12}>
    {/* Main content */}
  </Grid>
</Grid>
```

## Component Development Workflow

### ALWAYS START WITH A PLAN

Before implementing any feature or component:

1. **Create a TODO list** using TodoWrite tool
2. **Present the plan** to the human engineer for review
3. **Wait for approval** or course corrections
4. **Then proceed** with implementation

### Senior Developer Workflow Steps

As a senior React developer, ALWAYS follow these steps:

#### 1. Code Analysis Phase
- **Read and understand existing code** thoroughly before making changes
- **Identify all affected components** and their dependencies
- **Check for existing patterns** that solve similar problems
- **Review related API endpoints** and data structures
- **Understand the current user flow** and business logic

#### 2. Impact Analysis Phase
- **List all files that will be modified** or created
- **Identify potential breaking changes** to existing functionality
- **Check for shared components** that might be affected
- **Evaluate performance implications** of proposed changes
- **Consider backward compatibility** requirements
- **Assess impact on tests** and documentation

#### 3. Planning Phase
- **Create detailed technical plan** with specific implementation steps
- **Define component interfaces** and prop types
- **Plan state management** approach (local state, context, or store)
- **Design error handling** and edge cases
- **Plan for accessibility** and internationalization if needed
- **Estimate effort** and identify risks

#### 4. Review Phase
- **Present the analysis and plan** to stakeholders
- **Discuss trade-offs** and alternative approaches
- **Get alignment** on the technical approach
- **Address concerns** before starting implementation

#### 5. Implementation Phase
- **Follow the approved plan** systematically
- **Write clean, self-documenting code**
- **Implement proper error boundaries**
- **Add loading and error states**
- **Ensure proper TypeScript types**
- **Follow existing code patterns**

#### 6. Quality Assurance Phase
- **Test in both light and dark modes**
- **Verify responsive behavior**
- **Check accessibility** (keyboard navigation, screen readers)
- **Test error scenarios** and edge cases
- **Ensure no console errors** or warnings
- **Validate API integration** works correctly

### Example Analysis Template
```markdown
## Feature: [Feature Name]

### 1. Current State Analysis
- Existing components involved: [List components]
- Current data flow: [Describe current flow]
- API endpoints used: [List endpoints]

### 2. Impact Analysis
- Files to be modified: [List files]
- Breaking changes: [Yes/No - explain]
- Performance impact: [Describe impact]
- Dependencies affected: [List dependencies]

### 3. Technical Plan
- [ ] Step 1: [Specific action]
- [ ] Step 2: [Specific action]
- [ ] Step 3: [Specific action]

### 4. Risk Assessment
- Risks: [List potential risks]
- Mitigation: [How to handle risks]

### 5. Testing Strategy
- Unit tests needed: [List tests]
- Integration tests: [List tests]
- Manual testing: [List scenarios]
```

### Code Review Checklist
Before completing any feature:
- [ ] Code analysis completed
- [ ] Impact analysis documented
- [ ] Plan reviewed and approved
- [ ] No hardcoded values
- [ ] All strings use theme values
- [ ] TypeScript types are complete
- [ ] Error handling implemented
- [ ] Loading states added
- [ ] Responsive design verified
- [ ] Accessibility checked
- [ ] Both theme modes tested
- [ ] No console errors
- [ ] API integration tested
- [ ] Documentation updated if needed

## Project Structure Patterns

### Component Organization
```
/components
  /styled           # Styled components (modern.ts)
  /@extended        # Extended MUI components
  /cards           # Card components
  /filters         # Filter components

/sections          # Page sections
  /services        # Service-related sections
  /dashboard       # Dashboard sections
  
/themes           # Theme configuration
  /overrides      # MUI component overrides
  palette.js      # Color definitions
  typography.js   # Typography settings
```

### Import Order
1. React imports
2. External libraries (MUI, etc.)
3. Internal components
4. Utilities/hooks
5. Types
6. Styles

## Chip Component Specific Guidelines

When working with Chips:

```javascript
// Use MUI Chip color variants
<Chip color="primary" />
<Chip color="secondary" />
<Chip color="error" />

// Custom styled chips (from modern.ts)
<StatusChip status="active" />
<StatusChip status="pending" />

// Theme overrides are in /themes/overrides/Chip.js
```
## Before Making Changes

1. **Search for existing patterns**:
   - Use Grep to find similar components
   - Check /components/styled/modern.ts for styled components
   - Review theme overrides in /themes/overrides/

2. **Understand the theme structure**:
   - Main theme: /themes/theme/unizo.js
   - Palette: /themes/palette.js
   - Component overrides: /themes/overrides/

3. **Test in both modes**:
   - Always verify components work in light mode
   - Always verify components work in dark mode
   - Check responsive behavior

## Performance Best Practices

1. Use `useMemo` for expensive theme calculations
2. Avoid inline style objects in render
3. Use `styled()` for frequently used components
4. Leverage theme caching

## Form Field Design Patterns

### Required vs Optional Fields

Follow this consistent pattern for form field labels:

1. **Required Fields** - Display only the field name without any indicators
   ```javascript
   <Typography variant="body1" fontWeight={600} sx={{ mb: 0.5 }}>
     Field Name
   </Typography>
   ```

2. **Optional Fields** - Explicitly add "Optional" label
   ```javascript
   <Typography variant="body1" fontWeight={600} sx={{ mb: 0.5 }}>
     Field Name
     <Typography component="span" variant="caption" color="text.secondary" sx={{ ml: 1, fontWeight: 400 }}>
       Optional
     </Typography>
   </Typography>
   ```

### Form Field Structure

Standard form field layout:
```javascript
<Box>
  <Typography variant="body1" fontWeight={600} sx={{ mb: 0.5 }}>
    Field Label
    {/* Add Optional tag only for optional fields */}
  </Typography>
  <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 1 }}>
    Help text explaining the field
  </Typography>
  <Controller
    name="fieldName"
    control={control}
    render={({ field, fieldState }) => (
      <TextField
        {...field}
        fullWidth
        placeholder="Helpful placeholder"
        error={!!fieldState.error && fieldState.isDirty}
        helperText={fieldState.isDirty && fieldState.error?.message}
      />
    )}
  />
</Box>
```

### Key Principles:
- **No red asterisks** - The absence of "Optional" indicates a required field
- **Consistent spacing** - Use `mb: 0.5` for labels, `mb: 1` for help text
- **Show errors only when dirty** - Don't show validation errors on pristine fields
- **Clear help text** - Provide context about what the field is for

## Search Bar Styling Guidelines

### Consistent Search Input Design

All search inputs across the application should follow this theme-based pattern:

```javascript
<TextField
  placeholder="Search..."
  size="medium"
  sx={{ 
    flex: 1,
    width: { xs: '100%', sm: 'auto' },
    '& .MuiOutlinedInput-root': {
      backgroundColor: theme.palette.mode === 'dark' 
        ? alpha(theme.palette.background.paper, 0.8)
        : theme.palette.grey[50],
      minHeight: 40,
      borderRadius: 0,
      '& fieldset': {
        borderColor: theme.palette.divider,
        borderRadius: 0,
      },
      '&:hover': {
        backgroundColor: theme.palette.mode === 'dark' 
          ? alpha(theme.palette.background.paper, 0.8)
          : theme.palette.grey[50],
        '& .MuiOutlinedInput-notchedOutline': {
          borderColor: theme.palette.mode === 'dark'
            ? alpha(theme.palette.divider, 1.5)
            : theme.palette.grey[400],
        }
      },
      '&.Mui-focused': {
        backgroundColor: theme.palette.mode === 'dark' 
          ? alpha(theme.palette.background.paper, 0.8)
          : theme.palette.grey[50],
        '& .MuiOutlinedInput-notchedOutline': {
          borderColor: theme.palette.primary.main,
          borderWidth: '1px',
        }
      }
    },
    '& .MuiInputBase-input': {
      padding: '8px 12px',
    }
  }}
  InputProps={{
    startAdornment: (
      <InputAdornment position="start">
        <Search size={20} style={{ color: theme.palette.text.secondary }} />
      </InputAdornment>
    ),
  }}
/>
```

Key styling principles:
- **Background**: Light gray in light mode (`theme.palette.grey[50]`), semi-transparent paper in dark mode
- **Border**: Uses `theme.palette.divider` for default state
- **Hover**: Slightly darker border using theme colors
- **Focus**: Primary color border
- **Border radius**: No border radius (borderRadius: 0)
- **No hardcoded colors**: All colors come from theme

## Common Mistakes to Avoid

1. **Hardcoded colors** - Always use theme
2. **Pixel values** - Use theme.spacing()
3. **Inline styles** - Use sx prop or styled()
4. **Forgetting dark mode** - Test both modes
5. **Skipping the plan** - Always create TODOs first
6. **Not checking existing patterns** - Research before implementing
7. **Inconsistent border radius** - Use 8px for UI consistency
7. **Using asterisks for required fields** - Follow the implicit required pattern

## Code Review Checklist

Before completing any styling task:
- [ ] No hardcoded colors
- [ ] All spacing uses theme units
- [ ] Works in light mode
- [ ] Works in dark mode
- [ ] Follows existing patterns
- [ ] TypeScript types are proper
- [ ] Responsive behavior verified
- [ ] No console errors/warnings
- [ ] No emojis

## Debugging Tips

If styles aren't applying correctly:
1. Check theme provider wrapping
2. Verify import paths
3. Inspect computed styles in DevTools
4. Check for CSS specificity issues
5. Ensure proper theme mode is active

Remember: **Consistency is key!** When in doubt, look for existing patterns in the codebase and follow them.

## Table Design Patterns

### Standard Table Implementation

Always use the extended Table component for consistent styling and functionality.

#### Basic Table Setup
```javascript
import Table from 'components/@extended/Table';
import MainCard from 'components/MainCard';

// In your component
<MainCard sx={{ p: 0 }}>
  <Table
    data={tableData}
    columns={columns}
    manualPagination={false}
    enableSorting={false}
    enableFilters={false}
    enableColumnResizing={true}
  />
</MainCard>
```

#### Column Configuration Pattern
```javascript
const columns = useMemo(() => [
  {
    accessorKey: 'name',
    header: 'Name',
    minSize: 200,
    size: 250,
    cell({ row: { original } }) {
      return (
        <Stack spacing={0.5}>
          <Typography 
            variant="body2" 
            fontWeight={500}
            sx={{ fontSize: { xs: '0.813rem', sm: '0.875rem' } }}
          >
            {original.name}
          </Typography>
          <Typography 
            variant="caption" 
            color="text.secondary"
            sx={{ fontSize: { xs: '0.688rem', sm: '0.75rem' } }}
          >
            {original.description || 'No description'}
          </Typography>
        </Stack>
      );
    }
  },
  {
    accessorKey: 'actions',
    header: () => (
      <Typography sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>
        Actions
      </Typography>
    ),
    size: 120,
    minSize: 100,
    cell({ row: { original } }) {
      return (
        <Stack direction="row" spacing={0.25} alignItems="center">
          {/* Action buttons */}
        </Stack>
      );
    }
  }
], [dependencies]);
```

### Action Buttons Pattern

Use consistent action button styling across all tables:

```javascript
// View/Eye Button
<Tooltip title="View details">
  <IconButton
    size="small"
    onClick={handleViewClick}
    sx={{ 
      color: theme.palette.text.secondary,
      padding: { xs: '4px', sm: '8px' },
      '&:hover': {
        backgroundColor: alpha(theme.palette.primary.main, 0.08),
        color: theme.palette.primary.main,
      }
    }}
  >
    <VisibilityIcon sx={{ fontSize: { xs: 18, sm: 20 } }} />
  </IconButton>
</Tooltip>

// Test/Play Button
<Tooltip title="Test">
  <IconButton
    size="small"
    onClick={handleTestClick}
    sx={{ 
      color: theme.palette.text.secondary,
      padding: { xs: '4px', sm: '8px' },
      '&:hover': {
        backgroundColor: alpha(theme.palette.primary.main, 0.08),
        color: theme.palette.primary.main,
      }
    }}
  >
    <PlayArrowIcon sx={{ fontSize: { xs: 18, sm: 20 } }} />
  </IconButton>
</Tooltip>

// Edit Button (hidden on mobile)
<Tooltip title="Edit">
  <IconButton
    size="small"
    onClick={handleEditClick}
    sx={{ 
      color: theme.palette.text.secondary,
      padding: { xs: '4px', sm: '8px' },
      display: { xs: 'none', sm: 'inline-flex' },
      '&:hover': {
        backgroundColor: alpha(theme.palette.primary.main, 0.08),
        color: theme.palette.primary.main,
      }
    }}
  >
    <EditIcon sx={{ fontSize: { xs: 18, sm: 20 } }} />
  </IconButton>
</Tooltip>

// More Actions Menu
<IconButton
  size="small"
  onClick={handleMenuClick}
  sx={{ 
    color: theme.palette.text.secondary,
    padding: { xs: '4px', sm: '8px' },
    '&:hover': {
      backgroundColor: alpha(theme.palette.action.active, 0.08),
    }
  }}
>
  <MoreVert sx={{ fontSize: { xs: 18, sm: 20 } }} />
</IconButton>
<Menu
  anchorEl={anchorEl}
  open={open}
  onClose={handleClose}
  anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
  transformOrigin={{ vertical: 'top', horizontal: 'right' }}
  PaperProps={{
    sx: {
      mt: 0.5,
      minWidth: 120,
      boxShadow: theme.shadows[3],
    }
  }}
>
  <MenuItem onClick={handleEdit} sx={{ fontSize: '0.875rem' }}>
    <EditIcon fontSize="small" sx={{ mr: 1 }} />
    Edit
  </MenuItem>
  <MenuItem 
    onClick={handleDelete}
    sx={{ 
      fontSize: '0.875rem',
      color: theme.palette.error.main,
      '&:hover': {
        backgroundColor: alpha(theme.palette.error.main, 0.08),
      }
    }}
  >
    <DeleteIcon fontSize="small" sx={{ mr: 1 }} />
    Delete
  </MenuItem>
</Menu>
```

### Responsive Table Design

Tables should adapt to different screen sizes:

```javascript
// Responsive column sizing
{
  accessorKey: 'description',
  header: () => (
    <Typography sx={{ 
      fontSize: { xs: '0.75rem', sm: '0.875rem' },
      display: { xs: 'none', sm: 'block' } // Hide on mobile
    }}>
      Description
    </Typography>
  ),
  size: 200,
  minSize: 150,
  cell({ row: { original } }) {
    return (
      <Typography 
        variant="body2"
        sx={{ 
          display: { xs: 'none', sm: 'block' },
          fontSize: { xs: '0.75rem', sm: '0.875rem' }
        }}
      >
        {original.description}
      </Typography>
    );
  }
}
```

### Table Container Pattern

Always wrap tables in MainCard for consistent styling:

```javascript
<MainCard sx={{ p: 0, height: '100%' }}>
  <Table
    data={data}
    columns={columns}
    manualPagination={false}
    enableSorting={true}
    enableFilters={false}
    enableColumnResizing={true}
  />
</MainCard>
```

### Table with Side Panel Layout

For pages with detail panels:

```javascript
<Box sx={{ 
  display: 'flex',
  flexDirection: isMobile ? 'column' : 'row',
  height: 'calc(100vh - 300px)',
  minHeight: 500,
  gap: 2
}}>
  {/* Table Column */}
  <Box sx={{ 
    flex: showDetailsPanel ? '0 0 60%' : '1 1 auto',
    transition: 'flex 0.3s ease'
  }}>
    <MainCard sx={{ p: 0, height: '100%' }}>
      <Table {...tableProps} />
    </MainCard>
  </Box>
  
  {/* Details Panel Column */}
  {showDetailsPanel && (
    <Box sx={{ flex: '0 0 40%' }}>
      <DetailsPanel {...panelProps} />
    </Box>
  )}
</Box>
```

### Required Imports for Tables

```javascript
// Core table imports
import Table from 'components/@extended/Table';
import MainCard from 'components/MainCard';

// Action button icons (use MUI icons, not Lucide)
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import EditIcon from '@mui/icons-material/Edit';
import MoreVert from '@mui/icons-material/MoreVert';
import VisibilityIcon from '@mui/icons-material/Visibility';
import DeleteIcon from '@mui/icons-material/Delete';

// MUI components for actions
import { IconButton, Menu, MenuItem, Tooltip, Stack } from '@mui/material';
```

### Table Styling Rules

1. **Action Button Spacing**: Use `spacing={0.25}` in Stack for tight button grouping
2. **Icon Sizes**: Responsive sizes `{ xs: 18, sm: 20 }` for table icons
3. **Padding**: Use `padding: { xs: '4px', sm: '8px' }` for action buttons
4. **Colors**: Always use theme colors, never hardcode
5. **Hover States**: Use `alpha(theme.palette.primary.main, 0.08)` for hover backgrounds
6. **Typography**: Responsive font sizes for headers and content
7. **Mobile Behavior**: Hide less important columns on mobile screens
8. **Error Actions**: Use error color for delete actions with appropriate hover states

## Complete Theme Reference

### Primary Color Palette (Blue-Grey)

```javascript
primary: {
  50: '#f8fafc',    // Lightest
  100: '#edf2f7',
  200: '#d4e0ed',
  300: '#a9c2db',   // Light
  400: '#6f99c3',
  500: '#4a6584',
  600: '#21384f',   // Main (primary brand color)
  700: '#2a4765',
  800: '#1a2b3d',   // Dark
  900: '#0f1924',   // Darker
  950: '#0a111a'    // Darkest
}
```

### Secondary Color Palette
- Light mode: `#000000` (Black)
- Dark mode: `#ffffff` (White)
- Dynamically switches based on theme mode

### Tertiary Color Palette (Orange Accent)
```javascript
tertiary: {
  50: '#fef5ee',
  100: '#fde8d6',
  200: '#fac9a9',
  300: '#f6a372',   // Light
  400: '#f17238',
  500: '#f27013',   // Main orange
  600: '#e45a00',
  700: '#c94a00',   // Dark
  800: '#a33c00',
  900: '#7d2e00'    // Darker
}
```

### Semantic Colors
```javascript
success: '#10b981' // Green
error: '#ef4444'   // Red
warning: '#f59e0b' // Yellow
info: '#3b82f6'    // Blue
```

### Navigation Colors
```javascript
navigationColors: {
  background: '#21384f',      // Primary dark blue
  backgroundHover: '#2a4765',
  activeBackground: '#1a2b3d',
  text: '#ffffff',
  highlight: '#ea580c'        // Orange accent
}
```

### Typography Configuration

```javascript
// Font Weights
fontWeightLight: 300,
fontWeightRegular: 400,
fontWeightMedium: 500,
fontWeightSemiBold: 600,
fontWeightBold: 700

// Heading Sizes
h1: { fontSize: '2rem' },      // 32px
h2: { fontSize: '1.5rem' },    // 24px
h3: { fontSize: '1.25rem' },   // 20px
h4: { fontSize: '1.125rem' },  // 18px
h5: { fontSize: '1rem' },      // 16px
h6: { fontSize: '0.875rem' }   // 14px

// Body Text
body1: { fontSize: '0.9375rem' }, // 15px
body2: { fontSize: '0.875rem' }   // 14px
```

## Component Architecture Patterns

### Component File Structure
```
components/
├── ComponentName/
│   ├── index.tsx               // Re-export
│   ├── ComponentName.tsx       // Main component
│   ├── ComponentName.styles.ts // Styled components
│   └── ComponentName.types.ts  // TypeScript interfaces
```

### Styled Component Pattern
```typescript
// ComponentName.styles.ts
import { styled } from '@mui/material/styles';

export const StyledCard = styled(Card)(({ theme }) => ({
  backgroundColor: theme.palette.background.paper,
  borderRadius: theme.shape.borderRadius,
  padding: theme.spacing(3),
  transition: theme.transitions.create(['box-shadow', 'transform']),
  '&:hover': {
    boxShadow: theme.shadows[4],
    transform: 'translateY(-2px)',
  }
}));
```

### Route Component Pattern with Skeleton Loading
```typescript
export default function PageComponent() {
  return (
    <ClientOnly fallback={<DashboardSkeleton />}>
      {() => <Dashboard />}
    </ClientOnly>
  );
}
```

## Form Handling Patterns

### React Hook Form with Zod Validation
```typescript
import { Form, FormField, FormItem, FormControl, FormMessage } from 'components/@extended/Form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';

const formSchema = z.object({
  name: z.string().min(1, "Name is required"),
  email: z.string().email("Invalid email")
});

const form = useForm<z.infer<typeof formSchema>>({
  resolver: zodResolver(formSchema),
  defaultValues: { 
    name: '',
    email: '' 
  }
});

<Form {...form}>
  <form onSubmit={form.handleSubmit(onSubmit)}>
    <FormField
      control={form.control}
      name="name"
      render={({ field }) => (
        <FormItem>
          <FormLabel>Name</FormLabel>
          <FormControl>
            <TextField 
              {...field} 
              error={!!form.formState.errors.name}
              helperText={form.formState.errors.name?.message}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
    <Button type="submit" variant="contained">
      Submit
    </Button>
  </form>
</Form>
```

## Data Table Patterns

### FilterTable with React Table
```typescript
import { FilterTable } from 'components/tables/FilterTable';
import { FormItemType } from 'types/form';

const columns = useMemo(() => [
  {
    header: 'Name',
    accessorKey: 'name',
    cell: ({ row }) => (
      <Link to={`/details/${row.original.id}`}>
        <Typography color="primary">
          {row.original.name}
        </Typography>
      </Link>
    ),
    meta: {
      filter: {
        filterType: FormItemType.Text,
        placeholder: 'Search by name'
      }
    }
  },
  {
    header: 'Status',
    accessorKey: 'status',
    cell: ({ row }) => (
      <StatusChip status={row.original.status} />
    ),
    meta: {
      filter: {
        filterType: FormItemType.Select,
        options: [
          { value: 'active', label: 'Active' },
          { value: 'inactive', label: 'Inactive' }
        ]
      }
    }
  }
], []);

<FilterTable
  data={data}
  columns={columns}
  title="Table Title"
  totalData={totalCount}
  manualPagination
  state={{
    pagination: paginationState,
    columnFilters
  }}
  onPaginationChange={setPaginationState}
  onColumnFiltersChange={setColumnFilters}
/>
```

## State Management Patterns

### Zustand Store Pattern
```typescript
// store/user.tsx
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

interface UserStore {
  user: User | null;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  fetchUser: (id: string) => Promise<void>;
  setUser: (user: User) => void;
  resetUser: () => void;
}

const useUserStore = create<UserStore>()(
  devtools((set, get) => ({
    user: null,
    isLoading: false,
    error: null,
    
    fetchUser: async (id: string) => {
      set({ isLoading: true, error: null });
      try {
        const { data } = await userClient.getUser(id);
        set({ user: data, isLoading: false });
      } catch (error) {
        set({ 
          error: error.message || 'Failed to fetch user', 
          isLoading: false 
        });
      }
    },
    
    setUser: (user) => set({ user }),
    
    resetUser: () => set({ user: null, error: null })
  }))
);
```

### Context Pattern for Feature State
```typescript
// contexts/FeatureContext.tsx
interface FeatureContextType {
  state: FeatureState;
  updateState: (updates: Partial<FeatureState>) => void;
}

const FeatureContext = createContext<FeatureContextType | undefined>(undefined);

export const FeatureProvider: FC<{ children: ReactNode }> = ({ children }) => {
  const [state, setState] = useState<FeatureState>(initialState);
  
  const updateState = useCallback((updates: Partial<FeatureState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);
  
  return (
    <FeatureContext.Provider value={{ state, updateState }}>
      {children}
    </FeatureContext.Provider>
  );
};

export const useFeature = () => {
  const context = useContext(FeatureContext);
  if (!context) {
    throw new Error('useFeature must be used within FeatureProvider');
  }
  return context;
};
```

## API Integration Patterns

### React Query Hook Pattern
```typescript
// hooks/api/integration/useIntegration.tsx
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

export const useIntegration = () => {
  const queryClient = useQueryClient();
  
  // Query hook
  const useGetIntegrations = (params: SearchParams) => {
    return useQuery({
      queryKey: ['integrations', params],
      queryFn: () => IntegrationClient.search(params),
      staleTime: 5 * 60 * 1000, // 5 minutes
      keepPreviousData: true
    });
  };
  
  // Mutation for create
  const createIntegration = useMutation({
    mutationFn: IntegrationClient.create,
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['integrations'] });
      toast.success('Integration created successfully');
      return data;
    },
    onError: (error) => {
      toast.error(error.message || 'Failed to create integration');
    }
  });
  
  // Mutation for update
  const updateIntegration = useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateData }) => 
      IntegrationClient.update(id, data),
    onSuccess: (data, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['integrations'] });
      queryClient.invalidateQueries({ queryKey: ['integration', id] });
      toast.success('Integration updated successfully');
    }
  });
  
  return {
    useGetIntegrations,
    createIntegration,
    updateIntegration
  };
};
```

## Common Component Patterns

### Progressive Filter Bar
```typescript
import { ProgressiveFilterBar } from 'components/filters/ProgressiveFilterBar';

const filterFields = [
  {
    name: 'status',
    label: 'Status',
    type: FormItemType.Select,
    options: [
      { value: 'active', label: 'Active' },
      { value: 'inactive', label: 'Inactive' }
    ]
  },
  {
    name: 'createdDate',
    label: 'Created Date',
    type: FormItemType.DateRange
  }
];

<ProgressiveFilterBar
  fields={filterFields}
  filters={filters}
  onFiltersChange={setFilters}
  searchValue={searchValue}
  onSearchChange={setSearchValue}
  resultCount={data.length}
  placeholder="Search..."
/>
```

### Stat Card Component
```typescript
<ModernStatCard
  title="Total Revenue"
  value="$12,345"
  icon={<DollarIcon />}
  trend={15.3}
  trendLabel="vs last month"
  color="primary"
/>
```

### Empty State Component
```typescript
<EmptyState
  icon={<SearchOffIcon />}
  title="No results found"
  description="Try adjusting your search or filter criteria"
  action={
    <Button variant="contained" onClick={handleReset}>
      Reset Filters
    </Button>
  }
/>
```

## Navigation and Routing Patterns

### Remix Route Pattern
```typescript
// route.tsx
import { json, type LoaderArgs, type MetaFunction } from "@remix-run/node";
import { useLoaderData, useNavigate } from "@remix-run/react";

export const meta: MetaFunction = () => {
  return [
    { title: "Unizo - Page Title" },
    { name: "description", content: "Page description" }
  ];
};

export const loader = async ({ request, params }: LoaderArgs) => {
  // Authentication check
  const user = await requireUser(request);
  
  // Data fetching
  const data = await fetchData(params.id);
  
  return json({ user, data });
};

export const action = async ({ request }: ActionArgs) => {
  const formData = await request.formData();
  // Handle form submission
  return redirect("/success");
};

export default function RoutePage() {
  const { data } = useLoaderData<typeof loader>();
  const navigate = useNavigate();
  
  return <PageComponent data={data} />;
}
```

## Common Libraries and Technologies

### Core Technologies
- **Framework**: Remix (React-based)
- **UI Library**: Material-UI (MUI) v5
- **Styling**: @mui/material/styles, styled-components pattern
- **State Management**: Zustand for global state, React Context for local state
- **Forms**: react-hook-form with zod validation
- **Data Tables**: @tanstack/react-table v8
- **API Client**: React Query (@tanstack/react-query)
- **Date Handling**: date-fns, moment (legacy)
- **Notifications**: sonner (toast)
- **Icons**: @mui/icons-material, custom SVG icons

### Development Tools
- **TypeScript**: Strict mode enabled
- **Code Quality**: ESLint, Prettier
- **Testing**: Jest, React Testing Library
- **Build Tool**: Vite

## File Naming Conventions

### Components
- PascalCase for component files: `UserProfile.tsx`
- camelCase for utility files: `userHelpers.ts`
- kebab-case for route files: `user-profile.tsx`

### Folders
- kebab-case for feature folders: `user-management/`
- Special folders with @ prefix: `@extended/`, `@types/`

### Style Files
- `.styles.ts` suffix for styled components
- `.module.css` for CSS modules (if used)

## Testing Patterns

### Component Testing
```typescript
import { render, screen, fireEvent } from '@testing-library/react';
import { ThemeProvider } from '@mui/material/styles';
import { theme } from 'themes/theme';

const renderWithTheme = (component: ReactElement) => {
  return render(
    <ThemeProvider theme={theme}>
      {component}
    </ThemeProvider>
  );
};

describe('ComponentName', () => {
  it('should render correctly', () => {
    renderWithTheme(<ComponentName />);
    expect(screen.getByText('Expected Text')).toBeInTheDocument();
  });
});
```

## Known Issues to Fix

1. **Hardcoded Colors in modern.ts**: Several components use hardcoded hex values instead of theme colors
2. **Missing Dark Mode Support**: Some custom components don't properly support dark mode
3. **Inconsistent Spacing**: Some pages use mixed spacing units

## Best Practices Summary

1. **Always use theme values** - Never hardcode colors or spacing
2. **Plan before implementing** - Create TODO list first
3. **Follow existing patterns** - Check similar components before creating new ones
4. **Test both theme modes** - Ensure dark/light mode compatibility
5. **Use TypeScript strictly** - Define proper interfaces and types
6. **Optimize performance** - Use React Query for data fetching, memoization for expensive operations
7. **Maintain consistency** - Follow the established file structure and naming conventions
8. **Write clean code** - No comments unless requested, keep code self-documenting
9. **Handle errors gracefully** - Use toast notifications for user feedback
10. **Keep components small** - Break down complex UIs into smaller, reusable pieces