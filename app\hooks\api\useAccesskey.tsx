import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { accesskeyClient } from "services/access-key.service";
import Cookies from 'js-cookie'

import { API_ENDPOINTS } from "utils/api/api-endpoints";
import moment from "moment";
import { useEffect, useState } from "react";
import useInitialSetup from "store/initial-setup";
import useUserDetails from "store/user";

export type SPQueryOptionsType = {
   offset?: number;
   limit?: number;
   orderBy?: string;
};

export type ExpirationOption = {
   days: number;
   label: string;
};

const KEY_PREFIX = 'ACCESS_KEY_';

export const EXPIRATION_OPTIONS: ExpirationOption[] = [
   { days: 7, label: '7 days' },
   { days: 30, label: '30 days' },
   { days: 60, label: '60 days' },
   { days: 90, label: '90 days' },
   { days: -1, label: 'Custom' },
   { days: 0, label: 'No expiration' }
];

export const useAccesskey = () => {
   const [generatedKey, setGeneratedKey] = useState<string | null>(null);
   const { user } = useUserDetails();
   const orgId = user?.organization?.id;
   const queryClient = useQueryClient();
   
   // Generate keys using organization ID from user store
   const KEY = orgId ? `${KEY_PREFIX}${orgId}` : KEY_PREFIX;
   const EXPIRES_AT_KEY = `${KEY}_expires`;
   const CREATED_AT_KEY = `${KEY}_created`;
   const HAS_KEY = `${KEY}_exists`;

   // Fetch existing access keys from API
   const { data: accessKeys, isLoading: isLoadingKeys } = useQuery({
      queryKey: [API_ENDPOINTS.ACCESS_KEY, 'list'],
      queryFn: async () => {
         const response = await accesskeyClient.getAllAccessKeys();
         return response?.data || [];
      },
      staleTime: 5 * 60 * 1000, // 5 minutes
   });

   const {
      mutateAsync: generateKey,
      isPending,
   } = useMutation({
      mutationFn: accesskeyClient.createAccessKey,
      mutationKey: [API_ENDPOINTS.ACCESS_KEY],
      onSuccess: () => {
         // Invalidate and refetch the access keys list
         queryClient.invalidateQueries({ queryKey: [API_ENDPOINTS.ACCESS_KEY, 'list'] });
      }
   });
   
   const { setValues } = useInitialSetup();

   const generate = async (expirationDays: number = 30) => {
      const payload: any = { type: 'GENERATE_REQUEST' };
      
      // Add expiration date in UTC format when expiration days is set
      if (expirationDays > 0) {
         // Calculate future date and convert to UTC ISO format
         const expirationDateTime = moment()
            .add(expirationDays, 'days')
            .utc()
            .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]');
         payload.expirationDateTime = expirationDateTime;
      }
      
      const resp = await generateKey(payload);
      const data = resp?.data?.data?.[0];

      if (data?.key) {
         // Set the generated key for one-time display
         setGeneratedKey(data.key);
         
         // Store metadata only (not the actual key)
         const expirationDate = expirationDays > 0 
            ? moment().add(expirationDays, 'days')
            : null;
         
         Cookies.set(HAS_KEY, 'true', { expires: 365 });
         Cookies.set(EXPIRES_AT_KEY, expirationDate ? expirationDate.format('lll') : 'No expiration', { expires: 365 });
         Cookies.set(CREATED_AT_KEY, moment().format('lll'), { expires: 365 });
         
         // Set for initial setup flow
         setValues('accessKey', true);
         
         return data.key;
      }
   };

   const clearGeneratedKey = () => {
      setGeneratedKey(null);
   };

   // Use API data if available, fallback to cookies
   const firstKey = accessKeys?.[0];
   const AccessKey = accessKeys?.key;
   const hasApiKey = !!firstKey;

   // Determine data source - API takes precedence over cookies
   let hasKey = hasApiKey || Cookies.get(HAS_KEY) === 'true';
   let expiresAt = firstKey?.expirationDateTime ? moment(firstKey.expirationDateTime).format('lll') : Cookies.get(EXPIRES_AT_KEY);
   let createdAt = firstKey?.createdAt ? moment(firstKey.createdAt).format('lll') : Cookies.get(CREATED_AT_KEY);
   // Calculate expiration status
   const expirationMoment = firstKey?.expirationDateTime 
      ? moment(firstKey.expirationDateTime)
      : (expiresAt && expiresAt !== 'No expiration' ? moment(expiresAt) : null);
      
   const isExpired = expirationMoment ? expirationMoment.isBefore(moment()) : false;
   
   const daysRemaining = expirationMoment
      ? Math.max(0, expirationMoment.diff(moment(), 'days'))
      : null;

   useEffect(() => {
      setValues('accessKey', hasKey);
   }, [hasKey]);

   return {
      hasKey,
      AccessKey,
      generatedKey,
      expiresAt,
      createdAt,
      isPending: isPending || isLoadingKeys,
      generate,
      clearGeneratedKey,
      isExpired,
      daysRemaining,
      isLoadingKeys,
      apiKeyData: firstKey
   };
};
