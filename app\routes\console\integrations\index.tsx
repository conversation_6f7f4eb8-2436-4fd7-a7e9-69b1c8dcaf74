import { type MetaFunction } from "@remix-run/node";
import { ClientOnly } from 'remix-utils/client-only';
import { Link } from "@remix-run/react";
import { useEffect, useMemo, useState } from "react";
import { ColumnFiltersState } from "@tanstack/react-table";
import { Tooltip } from "@mui/material";
import Paper from "@mui/material/Paper";
import { TableSkeleton } from 'components/skeletons';

import { Button, InputAdornment, Stack, TextField, Typography, useTheme } from "@mui/material";

import { useTable } from "hooks/table/useTable";
import { useGetIntegration } from "hooks/api/integration/useGetIntegration";
import { useStatus } from "hooks/useStatus";
import _ from "lodash";
import { useServiceProfile } from "hooks/useServiceProfile";
import { useDate } from "hooks/useDate";
import FilterTable from "components/@extended/Table/filter-table";
import { ProgressiveFilterBar } from "components/filters/ProgressiveFilterBar";
import { DEFAULT_PAGINATION } from "constants/common";
import { FormItemType } from "constants/form";
import useUserDetails from "store/user";
import useIntegrationFilter from "sections/integrations/useFilter";
import { minDate, maxDate } from "utils/date-field";

const COLUMN_ID = {
   type: "type",
   provider: "serviceProfile.name",
   state: "state",
   health: "health.status",
   customer: "subOrganization.name",
};

export const meta: MetaFunction = () => {
   return [
      { title: "Unizo" },
   ];
};

export default function Integrations() {
   const { palette } = useTheme();
   const { user } = useUserDetails();

   const { renderStatus, resolveOutlinedIcon } = useStatus(),
      { loadImage } = useServiceProfile(),
      { loadDate } = useDate()

   const [paginationState, setPaginationState] = useState<any>(DEFAULT_PAGINATION);
   const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
   const [filters, setFilters] = useState<any[]>([]);
   const [searchValue, setSearchValue] = useState("");

   const {
      paginationModal: { pagination, setTotal },
   } = useTable()

   const selectedProvider: string = useMemo(() => {
      return columnFilters?.find((i) => i.id === COLUMN_ID.type)?.value as string;
   }, [columnFilters]);

   const { providerOptions, categoriesOptions, stateOptions, } = useIntegrationFilter({
      selectedProvider,
      columnFilters,
   });

   const { search, deriveRateLimit } = useGetIntegration({ orgId: user?.organization?.id });

   const { data: resp, refetch } = search({
      orgId: user?.organization?.id,
      filter: columnFilters,
      ...paginationState,
   });

   const data = resp?.data?.data,
      pagResp = resp?.data?.pagination;

   useEffect(() => {
      setTotal(pagResp?.total)
   }, [pagResp])

   // Map ProgressiveFilterBar filters to columnFilters
   useEffect(() => {
      const mapped = filters.map((f) => {
         let value = Array.isArray(f.value) ? f.value[0] : f.value;

         // Ensure state filter values are uppercased
         if (f.key === COLUMN_ID.state && typeof value === 'string') {
            value = value.toUpperCase();
         }

         if (f.key === "type") {
            const matched = categoriesOptions.find((opt) => opt.label === value);
            const matchedProvider = providerOptions.find((opt) => opt.label === value);
            if (matched?.value || matchedProvider?.value) value = matched?.value || matchedProvider?.value;
         }

         return {
            id: f.key,
            value,
            operator: f.operator, // Pass the operator from ProgressiveFilterBar
         };
      });

      setColumnFilters(mapped);
   }, [filters, categoriesOptions, providerOptions]);

   // Global search binding
   useEffect(() => {
      setColumnFilters((prev) => {
         const otherFilters = prev.filter((f) => f.id !== "global");
         if (searchValue) {
            return [...otherFilters, { id: "global", value: searchValue }];
         }
         return otherFilters;
      });
   }, [searchValue]);

   const columns = useMemo(
      () => [
         {
            header: 'Name',
            accessorKey: 'Name',
            cell({ row: { original: row } }: any) {
               const epochDateTime = row?.changeLog?.createdDateTime ? new Date(row?.changeLog.createdDateTime).getTime() : '';
               return (
                  <Link to={`/console/integrations/${row?.id}/${row?.serviceProfile?.id}`}>
                     <Stack className="text-ellipsis gap-1 hover:underline">
                        <Typography  >
                           {row?.name}
                        </Typography>
                        <Typography variant='caption' color="secondary.600">
                           {row?.id}
                        </Typography>
                     </Stack>
                  </Link>
               )
            },
            meta: {
               filter: {
                  filterType: FormItemType.Text,
               },
            },
         },
         {
            header: 'Category',
            accessorKey: COLUMN_ID.type,
            meta: {
               filter: {
                  filterType: FormItemType.Select,
                  options: categoriesOptions,
               },
            },
         },
         {
            header: 'Provider',
            accessorKey: COLUMN_ID.provider,
            cell: ({ row: { original } }: any) => {
               return (
                  (
                     loadImage(original?.serviceProfile,
                        { size: 'xSmall' }
                     )
                  )
               )
            },
            meta: {
               filter: {
                  filterType: FormItemType.Select,
                  options: providerOptions,
               },
            },
         },
         {
            header: 'Customer',
            accessorKey: COLUMN_ID.customer,
            cell: ({ row: { original } }: any) => {
               return original?.subOrganization?.name
            },
            meta: {
               filter: {
                  filterType: FormItemType.Text,
               },
            },
         },
         {
            header: 'Health',
            accessorKey: COLUMN_ID.health,
            cell: ({ row: { original } }: any) => {
               return resolveOutlinedIcon(original?.health?.status)
            },
            meta: {
               className: 'cell-center',
               filter: {
                  filterType: FormItemType.Select,
               },
            }
         },
         {
            header: 'Rate Limit',
            accessorKey: 'rate_limit',
            cell: ({ row: { original } }: any) => {
               return deriveRateLimit(original).element
            },
            meta: {
               injectCellProps: ({ row: { original } }: any) => {
                  return {
                     className: deriveRateLimit(original).className
                  }
               }
            }
         },
         {
            header: 'State',
            accessorKey: COLUMN_ID.state,
            cell: ({ row: { original } }: any) => {
               return renderStatus(original?.state)
            },
            meta: {
               filter: {
                  filterType: FormItemType.Select,
                  options: stateOptions,
               },
            },
         },
         {
            header: 'Date',
            accessorKey: 'changeLog.lastUpdatedDateTime',
            cell: ({ row: { original } }: any) => {
               return loadDate(original?.changeLog?.lastUpdatedDateTime)
            },
            meta: {
               filter: {
                  filterType: FormItemType.DateTime,
                  minDateTime: minDate({ filters: columnFilters }),
               },
            },
         },
      ],
      [categoriesOptions, providerOptions, stateOptions, columnFilters]
   );

   return (
      <ClientOnly fallback={<TableSkeleton rows={10} columns={8} title="Integrations" showActions />}>
         {() => {
            return (
               <Stack gap={2}>
                  {/* Unified Filter Bar + Table Block */}
                  <Paper elevation={2} sx={{ p: 3, mb: 3, borderRadius: 2 }}>
                     <ProgressiveFilterBar
                        fields={[
                           {
                              key: COLUMN_ID.state,
                              label: "State",
                              type: "enum",
                              options: stateOptions.map((opt) => opt.label),
                              useOnce: true,
                           },
                           {
                              key: "name",
                              label: "Integration Name",
                              type: "text",
                              useOnce: true,
                           },
                           {
                              key: COLUMN_ID.type,
                              label: "Category",
                              type: "enum",
                              options: categoriesOptions.map((opt) => opt.label),
                              multiSelect: true,
                              useOnce: true,
                           },
                           {
                              key: "serviceProfile/name",
                              label: "Provider",
                              type: "enum",
                              options: providerOptions.map((opt) => opt.label),
                              useOnce: true,
                           },
                           {
                              key: "changeLog/createdDateTime",
                              label: "Created Date",
                              type: "date",
                              useOnce: true,
                           },
                           {
                              key: "changeLog/lastUpdatedDateTime",
                              label: "Updated Date",
                              type: "date",
                              useOnce: true,
                           },
                        ]}
                        filters={filters}
                        onFiltersChange={setFilters}
                        searchValue={searchValue}
                        onSearchChange={setSearchValue}
                        resultCount={data?.length || 0}
                        placeholder="Search integrations..."
                     />

                     <FilterTable
                        data={data}
                        columns={columns}
                        totalData={pagination?.total}
                        onPaginationChange={setPaginationState}
                        state={{
                           pagination: {
                              pageIndex: paginationState.pageIndex,
                              pageSize: paginationState.pageSize,
                           },
                           columnFilters,
                        } as any}
                        manualFiltering={true}
                        onColumnFiltersChange={() => {}}
                        onReset={() => {
                           setFilters([]);
                           setSearchValue("");
                        }}
                        onReload={refetch}
                     />
                  </Paper>
               </Stack>
            )
         }}
      </ClientOnly>
   );
}
