import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Stack,
  IconButton,
  Alert,
  useTheme,
  alpha
} from '@mui/material';
import { X, Copy, Check } from 'lucide-react';
import { useState } from 'react';
import moment from 'moment';

interface KeyGenerationModalProps {
  open: boolean;
  onClose: () => void;
  apiKey: string;
  expirationDays: number;
}

export default function KeyGenerationModal({ 
  open, 
  onClose, 
  apiKey,
  expirationDays 
}: KeyGenerationModalProps) {
  const theme = useTheme();
  const [copied, setCopied] = useState(false);

  const handleCopy = () => {
    navigator.clipboard.writeText(apiKey);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

const getExpirationText = () => {
  if (expirationDays === 0) {
    return 'No expiration';
  }
  return `${expirationDays} days`;
};


  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          p: 1
        }
      }}
    >
      <DialogTitle sx={{ 
        position: 'relative',
        pb: 1,
        pr: 5
      }}>
        <Typography variant="h5" fontWeight={600}>
          API key generated
        </Typography>
        <IconButton
          onClick={onClose}
          aria-label="close"
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
            color: theme.palette.text.secondary,
            padding: 0.5,
            '&:hover': {
              backgroundColor: alpha(theme.palette.action.active, 0.08)
            }
          }}
        >
          <X size={18} />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ py: 2 }}>
        <Stack spacing={3}>
          <Alert severity="warning" sx={{ 
            backgroundColor: alpha(theme.palette.warning.main, 0.1),
            '& .MuiAlert-icon': {
              color: theme.palette.warning.main
            }
          }}>
            <Typography variant="body2" fontWeight={500}>
              Copy your api key below and record it somewhere safe.
            </Typography>
            <Typography variant="body2" sx={{ mt: 0.5 }}>
              You will not be able to recover this key after closing this window.
            </Typography>
          </Alert>

          <Box>
            <Box
              sx={{
                backgroundColor: theme.palette.mode === 'dark' 
                  ? alpha(theme.palette.background.paper, 0.8)
                  : theme.palette.grey[50],
                border: `1px solid ${theme.palette.divider}`,
                borderRadius: 1,
                p: 2,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                fontFamily: 'monospace',
                wordBreak: 'break-all'
              }}
            >
              <Typography 
                variant="body2" 
                component="code"
                sx={{ 
                  fontSize: '0.875rem',
                  letterSpacing: '0.025em',
                  color: theme.palette.text.primary,
                  flex: 1,
                  mr: 2
                }}
              >
                {apiKey}
              </Typography>
              <IconButton
                onClick={handleCopy}
                size="small"
                sx={{
                  color: copied ? theme.palette.success.main : theme.palette.text.secondary,
                  '&:hover': {
                    backgroundColor: alpha(theme.palette.primary.main, 0.08)
                  }
                }}
              >
                {copied ? <Check size={16} /> : <Copy size={16} />}
              </IconButton>
            </Box>
          </Box>

          <Box>
            <Typography variant="body2" color="text.secondary">
              <strong>Expiration:</strong> {getExpirationText()}
            </Typography>
          </Box>
        </Stack>
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 2 }}>
        <Button 
          onClick={onClose} 
          variant="contained"
          fullWidth
          sx={{ py: 1 }}
        >
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
}