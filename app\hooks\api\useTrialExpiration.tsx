import { useQuery } from '@tanstack/react-query';
import { parseISO, isAfter } from 'date-fns';
import useUserDetails from 'store/user';
import { State } from 'hooks/useStatus';
import { userClient } from 'services/user.service';

interface Subscription {
  id: string;
  name: string;
  state: string;
  cancelledDate: string;
}

interface SubscriptionResponse {
  data: Subscription[];
}

export const useTrialExpiration = () => {
  const { user } = useUserDetails();
  const organizationId = user?.organization?.id;
  
  const { data, isLoading, error } = useQuery({
    queryKey: ['trial-expiration', organizationId],
    queryFn: async () => {
      if (!organizationId) {
        return { data: [] };
      }
      
      const payload = {
        filter: {
          and: [
            {
              property: '/state',
              operator: '=',
              values: [State.ACTIVE]
            },
            {
              property: '/organization/id',
              operator: '=',
              values: [organizationId]
            }
          ]
        },
        pagination: {
          limit: 100,
          offset: 0
        }
      };
      
      const response = await userClient.searchSubscriptions(payload);
      return response.data;
    },
    enabled: !!organizationId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1
  });
  
  const getLatestExpirationDate = () => {
    if (!data?.data || data.data.length === 0) {
      return null;
    }
    
    // Filter active subscriptions and find the latest expiration date
    const activeSubscriptions = data.data.filter(sub => sub.state === 'ACTIVE' && sub.cancelledDate);
    
    if (activeSubscriptions.length === 0) {
      return null;
    }
    
    // Find the subscription with the latest cancelledDate
    const latestSubscription = activeSubscriptions.reduce((latest, current) => {
      const latestDate = parseISO(latest.cancelledDate);
      const currentDate = parseISO(current.cancelledDate);
      return isAfter(currentDate, latestDate) ? current : latest;
    });
    
    return latestSubscription.cancelledDate;
  };
  
  const isTrialExpired = () => {
    const latestExpirationDate = getLatestExpirationDate();
    if (!latestExpirationDate) {
      return false;
    }
    
    return isAfter(new Date(), parseISO(latestExpirationDate));
  };
  
  return {
    latestExpirationDate: getLatestExpirationDate(),
    isTrialExpired: isTrialExpired(),
    isLoading,
    error
  };
};