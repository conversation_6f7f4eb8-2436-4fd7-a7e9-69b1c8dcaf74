/* eslint-disable @typescript-eslint/no-explicit-any */
import { vitePlugin as remix } from "@remix-run/dev";
import { defineConfig } from "vite";
import tsconfigPaths from "vite-tsconfig-paths";
import path from 'path';
import dotenv from 'dotenv';

declare module "@remix-run/node" {
  interface Future {
    v3_singleFetch: true
  }
}

dotenv.config({
  path: `.env${process.env.NODE_ENV ? `.${process.env.NODE_ENV}` : ""}`,
});

export default defineConfig(({ mode }) => {

  let noLocalConfig: any = {
    noExternal: [],
  }

  if (['build'].includes(mode)) {
    noLocalConfig = {
      noExternal: [
        '@mui/material',
        '@mui/utils',
        '@mui/system',
        '@mui/styled-engine',
        '@mui/x-date-pickers',
        '@mui/x-internals',
        'react-idle-timer',
        '@mui/x-charts',
        '@mui/lab',
      ],
    };
  }

  return {
    define: {
    },
    server: {
      port: 8000,
    },
    plugins: [
      remix({
        future: {
          v3_fetcherPersist: true,
          v3_relativeSplatPath: true,
          v3_throwAbortReason: true,
          v3_singleFetch: true,
          v3_lazyRouteDiscovery: true,

        },
        routes(defineRoutes) {
          return defineRoutes((route) => {
            // registration page

            route("/tenant-onboarding", "routes/console/tenant/tenant-onboarding.tsx");

            route("/", "routes/console/index.tsx", () => {

              route("/console/dashboard", "routes/console/dashboard/index.tsx");
              route("/console/quick-start", "routes/console/quick-start.tsx");
              route("/console/logs", "routes/console/logs.tsx");
              route("/console/integrations", "routes/console/integrations/index.tsx");
              route("/console/integrations/:id/:spId", "routes/console/integrations/$id.$spId.tsx");
              route("/console/setup-integrations", "routes/console/integrations/setup/index.tsx",()=>{
                route("/console/setup-integrations/connectors", "routes/console/integrations/setup/services/index.tsx")
                route("/console/setup-integrations/webhooks", "routes/console/integrations/setup/bidirectional/index.tsx")
                route("/console/setup-integrations/health-checks", "routes/console/integrations/setup/healthChecks/index.tsx")
              });
              route("/console/environments", "routes/console/environments/index.tsx");

              // security routes
              route("/console/security", "routes/console/security/index.tsx", () => {
                route("/console/security/log-protection", "routes/console/security/log-protection/index.tsx", { index: true })
                route("/console/security/key-protection", "routes/console/security/key-protection/index.tsx")
              })

              // api key routes
              route("/console/api-key", "routes/console/configure/api-key/index.tsx");

              // webhooks route
              route("/console/webhooks", "routes/console/webhooks.tsx");

              route("/console/connect-UI", "routes/console/advanced/index.tsx")
              // Configuration now uses drawer instead of separate routes
              // route("/console/connect-UI/configuration", "routes/console/advanced/unizoDock/index.tsx")
              // route("/console/connect-UI/configuration/:mode/:id?", "routes/console/advanced/userflow.tsx")
              
              // field mappings route
              route("/console/field-mappings", "routes/console/field-mappings/index.tsx")

              // account settings routes
              route("/console/settings", "routes/console/settings/index.tsx");

              // profile settings routes
              route("/console/profile-settings", "routes/console/profile/account-settings/index.tsx");
            });

            route("/admin", "routes/admin/index.tsx", () => {
              route("/admin/", "routes/admin/tenants/index.tsx");
              route("/admin/:id", "routes/admin/tenants/details/index.tsx");
              route("/admin/:id/subscription/:subscriptionId", "routes/admin/tenants/details/subscription/index.tsx");
            }),
              route("/admin/tenants/create", "routes/admin/tenants/create/index.tsx")
          });
        },
      }),

      tsconfigPaths(),
    ],
    ssr: {
      noExternal: [
        "@mui/icons-material",
        'prism-react-renderer',
        'react-idle-timer',
        ...(noLocalConfig?.noExternal || [])
      ], // bundle icons package for SSR
      external: ['axios']
    },
    resolve: {
      alias: [
        {
          find: /^~(.+)/,
          replacement: path.join(process.cwd(), 'node_modules/$1')
        },
        {
          find: /^app(.+)/,
          replacement: path.join(process.cwd(), 'src/$1')
        },
        {
          find: /^events/,
          replacement: path.join(process.cwd(), 'app/events'),
        },
        {
          find: /^hooks/,
          replacement: path.join(process.cwd(), 'app/hooks'),
        },
        {
          find: /^components/,
          replacement: path.join(process.cwd(), 'app/components'),
        },
        {
          find: /^config/,
          replacement: path.join(process.cwd(), 'app/config'),
        },
        {
          find: /^api/,
          replacement: path.join(process.cwd(), 'app/api'),
        },
        {
          find: /^menu-items/,
          replacement: path.join(process.cwd(), 'app/menu-items'),
        },
        {
          find: /^layout/,
          replacement: path.join(process.cwd(), 'app/layout'),
        },
        {
          find: /^contexts/,
          replacement: path.join(process.cwd(), 'app/contexts'),
        },
        {
          find: /^utils/,
          replacement: path.join(process.cwd(), 'app/utils'),
        },
        {
          find: /^themes/,
          replacement: path.join(process.cwd(), 'app/themes'),
        },
        {
          find: /^store/,
          replacement: path.join(process.cwd(), 'app/store'),
        },
        {
          find: /^sections/,
          replacement: path.join(process.cwd(), 'app/sections'),
        },
        {
          find: /^constants/,
          replacement: path.join(process.cwd(), 'app/constants'),
        },
        {
          find: /^hoc/,
          replacement: path.join(process.cwd(), 'app/hoc'),
        },
        {
          find: /^lib/,
          replacement: path.join(process.cwd(), 'app/lib'),
        }
      ]
    },
    css: {
      preprocessorOptions: {
        less: {
          javascriptEnabled: true, // Required for Ant Design
        }
      }
    }
  }
});