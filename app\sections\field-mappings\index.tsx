import { useState, useEffect, useMemo, useCallback, Suspense, lazy } from 'react';
import { 
  Typo<PERSON>, 
  Button, 
  Box,
  Grid,
  Stack,
  useTheme,
  alpha,
  Paper,
  Chip,
  IconButton,
  Tooltip,
  Tabs,
  Tab,
  Badge,
  TextField,
  InputAdornment,
  useMediaQuery,
  FormControl,
  Select,
  MenuItem,
  CircularProgress,
  Collapse,
  Alert
} from '@mui/material';
import { 
  Plus, 
  GitBranch, 
  GitCommit, 
  Users, 
  FolderGit, 
  FileText,
  ChevronRight,
  Zap,
  Package,
  Clock,
  ChevronDown,
  Search,
  AlertTriangle,
  RefreshCw,
  Settings2,
  Shapes,
  Eye
} from 'lucide-react';
import { useNavigate } from '@remix-run/react';
import { useQuery, useQueries, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

import PageCard from 'components/cards/PageCard';
import LearnMoreLink from 'components/@extended/LearnMoreLink';
import useGetAdditionalAttributes from 'hooks/api/additional-attributes';
import useGetFieldMappings from 'hooks/api/use-field-mappings';
import useUserDetails from 'store/user';
import { useGetServiceProfile } from 'hooks/api/service-profile/useGetServiceProfile';
import { DOMAINS, getDomainByKey } from 'data/domains';

// Lazy load components for better performance
const FieldMappingDrawer = lazy(() => import('./FieldMappingDrawer'));
import FieldMappingsSkeleton from './FieldMappingsSkeleton';
import FieldMappingsErrorBoundary from './FieldMappingsErrorBoundary';
import { dataModelsByCategory } from 'sections/services/category-fields/AdditionalFields/data-sets';
import AddFieldDialog from 'sections/services/category-fields/AdditionalFields/AddFieldDialog';

// Simple debounce utility
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null;
  
  return (...args: Parameters<T>) => {
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(() => {
      func(...args);
    }, wait);
  };
}

// Helper functions for model configuration
const formatModelName = (name: string) => {
  return name
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

// Select appropriate icon based on model name
const getModelIcon = (name: string) => {
  if (name.includes('organization')) return <Users size={20} />;
  if (name.includes('repository')) return <FolderGit size={20} />;
  if (name.includes('pull_request')) return <GitBranch size={20} />;
  if (name.includes('branch')) return <GitBranch size={20} />;
  if (name.includes('commit')) return <GitCommit size={20} />;
  if (name.includes('project')) return <FolderGit size={20} />;
  if (name.includes('issue')) return <FileText size={20} />;
  if (name.includes('sprint')) return <Zap size={20} />;
  if (name.includes('ticket')) return <FileText size={20} />;
  if (name.includes('incident')) return <FileText size={20} />;
  if (name.includes('change')) return <FileText size={20} />;
  return <FileText size={20} />; // Default icon
};

// Generate description based on model name
const getModelDescription = (name: string) => {
  if (name.includes('organization')) return 'Organization-level data and metadata';
  if (name.includes('repository')) return 'Repository information and settings';
  if (name.includes('pull_request')) return 'Pull request details and workflow data';
  if (name.includes('branch')) return 'Branch information and metadata';
  if (name.includes('commit')) return 'Commit data and change information';
  if (name.includes('project')) return 'Project structure and configuration';
  if (name.includes('issue')) return 'Issue tracking and management';
  if (name.includes('sprint')) return 'Sprint planning and progress';
  if (name.includes('ticket')) return 'Support ticket information';
  if (name.includes('incident')) return 'Incident reports and tracking';
  if (name.includes('change')) return 'Change request management';
  return 'Custom fields for this model';
};

// Model configuration - dynamically populated from API data
const getModelConfig = (model: string, category?: string): { name: string; icon: React.ReactNode; description: string } => {
  // Try to get config from dataModelsByCategory first
  if (category && dataModelsByCategory[category]) {
    const modelConfig = dataModelsByCategory[category].find(m => m.name === model);
    if (modelConfig) {
      return {
        name: modelConfig.displayName,
        icon: getModelIcon(model),
        description: modelConfig.description
      };
    }
  }
  
  return {
    name: formatModelName(model),
    icon: getModelIcon(model),
    description: getModelDescription(model)
  };
};

const FieldMappingsPage = () => {
  const theme = useTheme();
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const { user, subscriptions } = useUserDetails();
  
  // Responsive detection
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.down('md'));
  
  // State management
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [expandedModels, setExpandedModels] = useState<Record<string, boolean>>({});
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedModel, setSelectedModel] = useState<string | null>(null);
  const [drawerCategory, setDrawerCategory] = useState<string | null>(null);
  const [isSearching, setIsSearching] = useState(false);
  
  // Fetch custom fields with retry
  const { getAllAdditionalAttributesQuery } = useGetAdditionalAttributes();
  const { 
    data: customFieldsData, 
    isLoading, 
    error,
    refetch: refetchCustomFields 
  } = useQuery({
    ...getAllAdditionalAttributesQuery(),
    retry: 2,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
    onError: (error) => {
      toast.error('Failed to load custom fields');
      console.error('Custom fields fetch error:', error);
    }
  });
  
  // Fetch all services to get field mapping counts
  const { serviceProfileClient } = useGetServiceProfile({
    searchOptions: {
      search: {
        limit: 100,
        offset: 0
      }
    }
  });
  const { 
    data: services = [], 
    isLoading: servicesLoading 
  } = serviceProfileClient;
  
  // Fetch field mappings for all services
  const { getAllFieldMappings } = useGetFieldMappings();
  const fieldMappingQueries = useQueries({
    queries: services
      .filter(service => service.service?.state === 'ACTIVE')
      .map(service => ({
        ...getAllFieldMappings({ 
          serviceId: service.service?.id || '', 
          params: {} 
        }),
        enabled: !!service.service?.id
      }))
  });
  
  // Aggregate field mapping counts by category
  const fieldMappingCounts = useMemo(() => {
    const counts: Record<string, number> = {};
    
    fieldMappingQueries.forEach((query, index) => {
      if (query.data?.data?.data) {
        const service = services[index];
        const category = service?.type;
        if (category) {
          const mappingsCount = query.data.data.data.reduce((total: number, mapping: any) => {
            return total + (mapping.mappings ? Object.keys(mapping.mappings).length : 0);
          }, 0);
          counts[category] = (counts[category] || 0) + mappingsCount;
        }
      }
    });
    
    return counts;
  }, [fieldMappingQueries, services]);
  
  // Get subscribed categories from user subscriptions
  const subscribedCategories = useMemo(() => {
    if (!subscriptions || subscriptions.length === 0) return [];
    
    // Get unique domain keys from subscriptions
    const categories = new Set<string>();
    
    subscriptions.forEach(sub => {
      const productCode = sub.product?.code;
      
      // Debug log
      console.log('Processing subscription:', { productCode, subscription: sub });
      
      // Find the domain that matches this subscription
      const matchingDomain = DOMAINS.find(domain => {
        // Check if the subscription code matches the domain's subscription code
        if (domain.subscription?.code === productCode) return true;
        
        // Check if the product code matches the domain key
        if (domain.key === productCode?.toUpperCase()) return true;
        
        // Check external keys for synthetic events (like SyntacticBasedEvent)
        if (productCode?.includes('SyntacticBasedEvent') && domain.key === 'SCM') return true;
        
        // Also check if the subscription type matches
        if (sub.type === domain.key) return true;
        
        return false;
      });
      
      if (matchingDomain) {
        categories.add(matchingDomain.key);
      }
    });
    
    return Array.from(categories);
  }, [subscriptions]);
  
  // Process custom fields by category and model - including all models from dataModelsByCategory
  const fieldsByCategory = useMemo(() => {
    const fields = customFieldsData?.data?.data || [];
    const result: Record<string, Record<string, any[]>> = {};
    
    // Initialize all subscribed categories with all models from dataModelsByCategory
    subscribedCategories.forEach(category => {
      result[category] = {};
      
      // Add all models for this category from dataModelsByCategory
      const categoryModels = dataModelsByCategory[category];
      if (categoryModels) {
        categoryModels.forEach(model => {
          // Use the model's name property as the key
          result[category][model.name] = [];
        });
      }
    });
    
    // Populate with actual fields
    fields.forEach(field => {
      const category = field.category?.type;
      const model = field.dataModel?.type || 'general';
      
      // Only include fields from subscribed categories
      if (category && subscribedCategories.includes(category)) {
        if (!result[category][model]) {
          result[category][model] = [];
        }
        result[category][model].push(field);
      }
    });
    
    return result;
  }, [customFieldsData, subscribedCategories]);
  
  // Check if any custom fields exist across all subscribed categories
  const hasCustomFields = customFieldsData?.data?.data && customFieldsData.data.data.length > 0;
  
  // Always show the field mappings page if user has subscriptions
  const shouldShowFieldMappings = subscribedCategories.length > 0;
  
  // Check if there are any custom fields across all categories
  const hasAnyCustomFieldsInCategories = useMemo(() => {
    if (!fieldsByCategory || Object.keys(fieldsByCategory).length === 0) return false;
    
    // Check if any category has at least one custom field
    return Object.values(fieldsByCategory).some(models => 
      Object.values(models).some(fields => fields.length > 0)
    );
  }, [fieldsByCategory]);

  // Debounced search handler
  const debouncedSearch = useMemo(
    () => debounce((value: string) => {
      setIsSearching(false);
    }, 300),
    []
  );

  const handleSearchChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setSearchQuery(value);
    setIsSearching(true);
    debouncedSearch(value);
  }, [debouncedSearch]);

  // Optimized callbacks
  const handleCreateMapping = useCallback((category?: string, model?: string) => {
    if (category) {
      setDrawerCategory(category);
    } else if (subscribedCategories.length > 0) {
      // Use the currently active tab's category
      setDrawerCategory(subscribedCategories[activeTab]);
    }
    if (model) {
      setSelectedModel(model);
    }
    setDrawerOpen(true);
  }, [subscribedCategories, activeTab]);

  // Handler for creating custom field without pre-selected category (for empty state)
  const handleCreateCustomField = useCallback(() => {
    setDrawerCategory(null); // Don't pre-select any category
    setSelectedModel(null);  // Don't pre-select any model
    setDrawerOpen(true);
  }, []);

  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const [selectedField, setSelectedField] = useState<any>(null);
  const [viewCategory, setViewCategory] = useState<string>('');

  const handleViewField = useCallback((category: string, field: any) => {
    // Open the AddFieldDialog in view mode for the specific field
    setViewCategory(category);
    setSelectedField(field);
    setViewDialogOpen(true);
  }, []);

  const handleViewDialogClose = useCallback(() => {
    setViewDialogOpen(false);
    setSelectedField(null);
    setViewCategory('');
  }, []);

  const handleDrawerClose = useCallback(() => {
    setDrawerOpen(false);
    setDrawerCategory(null);
    setSelectedModel(null);
  }, []);

  const handleSaveMapping = async (_data: any) => {
    // Here you would typically make an API call to save the mapping
    console.log('Saving field mapping:', _data);
    // After successful save, you might want to refresh the mappings list
  };
  
  // Get category display configuration from domains
  const getCategoryLabel = (category: string): string => {
    const domain = getDomainByKey(category);
    return domain?.name || category;
  };
  
  // Set active tab when subscriptions load and initialize expanded state
  useEffect(() => {
    if (subscribedCategories.length > 0 && activeTab === 0) {
      // Default to first tab (0) since we're now showing subscribed categories only
      setActiveTab(0);
    }
    
    // Initialize models - expand if they have custom fields, collapse if empty
    const initialExpanded: Record<string, boolean> = {};
    subscribedCategories.forEach(category => {
      const categoryModels = dataModelsByCategory[category];
      const categoryFields = fieldsByCategory[category] || {};
      
      if (categoryModels) {
        categoryModels.forEach(model => {
          const modelKey = `${category}-${model.name}`;
          // Expand if model has custom fields, collapse if empty
          const hasFields = categoryFields[model.name] && categoryFields[model.name].length > 0;
          initialExpanded[modelKey] = hasFields;
        });
      }
    });
    setExpandedModels(initialExpanded);
  }, [subscribedCategories, fieldsByCategory]);
  
  const toggleModelExpansion = (modelKey: string) => {
    setExpandedModels(prev => ({
      ...prev,
      [modelKey]: !prev[modelKey]
    }));
  };

  // Render expandable model section - Memoized for performance
  const renderModelSection = useCallback((model: string, fields: any[], category: string, isLast: boolean) => {
    const config = getModelConfig(model, category);
    const modelKey = `${category}-${model}`;
    const isExpanded = expandedModels[modelKey] === true; // Default to false
    
    // Filter fields based on search
    const filteredFields = fields.filter(field => {
      if (!searchQuery || isSearching) return true;
      const query = searchQuery.toLowerCase();
      return (
        field.name?.toLowerCase().includes(query) ||
        field.key?.toLowerCase().includes(query) ||
        field.description?.toLowerCase().includes(query)
      );
    });
    
    return (
      <Box
        key={model}
        sx={{
          backgroundColor: theme.palette.background.paper,
          border: `1px solid ${theme.palette.divider}`,
          borderRadius: 1,
          mb: isLast ? 0 : 2, // Add margin bottom except for last item
          overflow: 'hidden',
          transition: theme.transitions.create(['box-shadow', 'border-color'], {
            duration: theme.transitions.duration.short,
          })
        }}
      >
        {/* Header */}
        <Box
          sx={{
            py: 2,
            px: 3,
            cursor: 'pointer',
            backgroundColor: alpha(theme.palette.action.hover, 0.02),
            '&:hover': {
              backgroundColor: alpha(theme.palette.action.hover, 0.06)
            }
          }}
          onClick={() => toggleModelExpansion(modelKey)}
        >
          <Stack direction="row" alignItems="center" justifyContent="space-between">
            <Stack direction="row" alignItems="center" spacing={2}>
              <IconButton
                size="small"
                aria-expanded={isExpanded}
                aria-label={`${isExpanded ? 'Collapse' : 'Expand'} ${config.name} section`}
                sx={{
                  transform: isExpanded ? 'rotate(90deg)' : 'rotate(0deg)',
                  transition: 'transform 0.3s'
                }}
              >
                <ChevronRight size={20} />
              </IconButton>
              <Box>
                <Typography variant="body1" fontWeight={600}>
                  {config.name}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  {config.description}
                </Typography>
              </Box>
            </Stack>
            <Stack direction="row" alignItems="center" spacing={2}>
              <Typography variant="body2" color="text.secondary">
                {fields.length} Custom Field{fields.length !== 1 ? 's' : ''}
              </Typography>
              <Button
                size="small"
                variant="outlined"
                startIcon={<Settings2 size={14} />}
                onClick={(e) => {
                  e.stopPropagation(); // Prevent accordion toggle
                  handleCreateMapping(category, model);
                }}
                sx={{
                  textTransform: 'none',
                  fontSize: '0.875rem',
                  px: 2,
                  py: 0.5,
                  minHeight: 28
                }}
              >
                Configure
              </Button>
            </Stack>
          </Stack>
        </Box>
        
        {/* Expanded Content */}
        {isExpanded && (
          <Box
            sx={{
              borderTop: `1px solid ${theme.palette.divider}`,
              backgroundColor: theme.palette.background.paper
            }}
          >
            {filteredFields.length > 0 ? (
              <Stack spacing={0} divider={<Box sx={{ borderBottom: `1px solid ${theme.palette.divider}` }} />}>
                {filteredFields.map((field) => {
                    return (
                    <Box
                      key={field.id}
                      sx={{
                        py: 2,
                        px: 2,
                        '&:hover': {
                          backgroundColor: alpha(theme.palette.action.hover, 0.02)
                        }
                      }}
                    >
                      <Stack direction="row" justifyContent="space-between" alignItems="flex-start" spacing={2}>
                        {/* Field info */}
                        <Stack spacing={0.75} flex={1}>
                          {/* Name (key) and data type on same line */}
                          <Stack direction="row" alignItems="center" spacing={1.5}>
                            <Stack direction="row" alignItems="center" spacing={0.5}>
                              <Typography variant="body2" fontWeight={600}>
                                {field.name}
                              </Typography>
                              <Typography 
                                variant="caption" 
                                color="text.secondary"
                                sx={{ 
                                  fontFamily: '"SF Mono", Monaco, "Cascadia Code", monospace',
                                  fontWeight: 400
                                }}
                              >
                                ({field.key})
                              </Typography>
                            </Stack>
                            <Chip
                              size="small"
                              label={field.dataType?.type || 'string'}
                              sx={{
                                height: 20,
                                fontSize: '0.7rem',
                                backgroundColor: alpha(theme.palette.action.hover, 0.7),
                                color: theme.palette.text.primary,
                                fontWeight: 400,
                                '& .MuiChip-label': {
                                  px: 1
                                }
                              }}
                            />
                          </Stack>
                          {/* Description */}
                          <Typography 
                            variant="caption" 
                            color="text.secondary"
                            sx={{ 
                              display: 'block',
                              lineHeight: 1.6,
                              fontStyle: field.description ? 'normal' : 'italic'
                            }}
                          >
                            {field.description || 'No description'}
                          </Typography>
                        </Stack>
                        
                        {/* View Action */}
                        <Tooltip title="View field details">
                          <IconButton
                            size="small"
                            onClick={() => handleViewField(category, field)}
                            sx={{
                              color: theme.palette.text.secondary,
                              '&:hover': {
                                backgroundColor: alpha(theme.palette.primary.main, 0.08),
                                color: theme.palette.primary.main,
                              }
                            }}
                          >
                            <Eye size={16} />
                          </IconButton>
                        </Tooltip>
                      </Stack>
                    </Box>
                  );
                })}
              </Stack>
            ) : searchQuery && fields.length > 0 ? (
              // No search results
              <Box sx={{ px: 3, py: 3 }}>
                <Stack spacing={2} alignItems="center">
                  <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center' }}>
                    No fields match your search "{searchQuery}"
                  </Typography>
                  <Button
                    size="small"
                    variant="text"
                    onClick={() => setSearchQuery('')}
                    sx={{
                      textTransform: 'none',
                      fontSize: '0.875rem',
                    }}
                  >
                    Clear search
                  </Button>
                </Stack>
              </Box>
            ) : (
              // No fields at all
              <Box sx={{ px: 3, py: 3 }}>
                <Stack spacing={2} alignItems="center">
                  <Typography variant="body1" fontWeight={500} color="text.primary">
                    No custom fields configured
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', maxWidth: 400 }}>
                    Configure custom fields and their mappings to your connected services
                  </Typography>
                </Stack>
              </Box>
            )}
          </Box>
        )}
      </Box>
    );
  }, [expandedModels, theme, searchQuery, isSearching, handleCreateMapping]);

  return (
    <>
      <Grid container spacing={3}>
        {/* Main Content */}
        <Grid item xs={12}>
          {error ? (
            // Error state
            <PageCard>
              <Box 
                sx={{ 
                  textAlign: 'center', 
                  py: { xs: 6, sm: 8 },
                  px: { xs: 2, sm: 4 },
                  maxWidth: 500,
                  mx: 'auto'
                }}
              >
                <Box
                  sx={{
                    width: 64,
                    height: 64,
                    borderRadius: '50%',
                    backgroundColor: alpha(theme.palette.error.main, 0.1),
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    mx: 'auto',
                    mb: 3
                  }}
                >
                  <FileText size={32} color={theme.palette.error.main} />
                </Box>
                
                <Typography variant="h6" fontWeight={600} gutterBottom>
                  Failed to Load Custom Fields
                </Typography>
                
                <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                  We couldn't fetch your custom field configuration. Please check your connection and try again.
                </Typography>
                
                <Stack direction="row" spacing={2} justifyContent="center">
                  <Button
                    variant="outlined"
                    onClick={() => refetchCustomFields()}
                    startIcon={<RefreshCw size={16} />}
                    sx={{ textTransform: 'none' }}
                  >
                    Retry
                  </Button>
                  <Button
                    variant="text"
                    onClick={() => window.location.reload()}
                    sx={{ textTransform: 'none' }}
                  >
                    Refresh Page
                  </Button>
                </Stack>
              </Box>
            </PageCard>
          ) : isLoading || servicesLoading ? (
            // Loading state - use dedicated skeleton component
            <FieldMappingsSkeleton />
          ) : subscribedCategories.length === 0 ? (
            // No subscriptions
            <PageCard>
              <Box 
                sx={{ 
                  textAlign: 'center', 
                  py: { xs: 6, sm: 8 },
                  px: { xs: 2, sm: 4 },
                  maxWidth: 500,
                  mx: 'auto'
                }}
              >
                <Box
                  sx={{
                    width: 64,
                    height: 64,
                    borderRadius: '50%',
                    backgroundColor: alpha(theme.palette.warning.main, 0.1),
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    mx: 'auto',
                    mb: 3
                  }}
                >
                  <Package size={32} color={theme.palette.warning.main} />
                </Box>
                
                <Typography variant="h6" fontWeight={600} gutterBottom>
                  No Active Subscriptions
                </Typography>
                
                <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                  You need an active subscription to configure field mappings. Please contact your administrator.
                </Typography>
              </Box>
            </PageCard>
          ) : shouldShowFieldMappings && !hasAnyCustomFieldsInCategories ? (
            // Empty state when user has subscriptions but no custom fields
            <PageCard>
              <Box 
                sx={{ 
                  textAlign: 'center', 
                  py: { xs: 6, sm: 8 },
                  px: { xs: 2, sm: 4 },
                  maxWidth: 600,
                  mx: 'auto'
                }}
              >
                <Box
                  sx={{
                    width: 64,
                    height: 64,
                    borderRadius: '50%',
                    backgroundColor: alpha(theme.palette.primary.main, 0.1),
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    mx: 'auto',
                    mb: 3
                  }}
                >
                  <Shapes size={32} color={theme.palette.primary.main} />
                </Box>
                
                <Typography variant="h5" fontWeight={600} gutterBottom>
                  Configure your first Custom Unified Field
                </Typography>
                
                <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
                  Add custom fields to the schemas to capture additional information specific to your business needs.
                </Typography>
                
                <Button
                  variant="contained"
                  startIcon={<Plus />}
                  onClick={handleCreateCustomField}
                  sx={{ 
                    textTransform: 'none',
                    fontWeight: 500,
                    px: 3
                  }}
                >
                  Create Custom Field
                </Button>
              </Box>
            </PageCard>
          ) : shouldShowFieldMappings ? (
            // Active state - show categories and models
            <Stack spacing={3}>
              {/* Tabs for desktop, Select for mobile */}
              {isMobile ? (
                <FormControl fullWidth variant="outlined" size="small">
                  <Select
                    value={activeTab}
                    onChange={(e) => setActiveTab(Number(e.target.value))}
                    sx={{ backgroundColor: theme.palette.background.paper }}
                    aria-label="Select category"
                  >
                    {subscribedCategories.map((category, index) => {
                      const categoryLabel = getCategoryLabel(category);
                      const categoryFields = fieldsByCategory[category] || {};
                      const totalFields = Object.values(categoryFields).flat().length;
                      const mappingCount = fieldMappingCounts[category] || 0;
                      
                      return (
                        <MenuItem key={category} value={index}>
                          <Stack direction="row" spacing={1} sx={{ width: '100%' }}>
                            <Typography>{categoryLabel}</Typography>
                            {totalFields > 0 && (
                              <Chip
                                label={totalFields}
                                size="small"
                                sx={{ height: 20, minWidth: 20 }}
                              />
                            )}
                            {mappingCount > 0 && (
                              <Badge badgeContent={mappingCount} color="success" />
                            )}
                          </Stack>
                        </MenuItem>
                      );
                    })}
                  </Select>
                </FormControl>
              ) : (
                <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                  <Tabs 
                    value={activeTab} 
                    onChange={(e, newValue) => setActiveTab(newValue)}
                    variant={isTablet ? "scrollable" : "standard"}
                    scrollButtons={isTablet ? "auto" : false}
                    allowScrollButtonsMobile
                    sx={{
                      '& .MuiTab-root': {
                        textTransform: 'none',
                        fontWeight: 500,
                        fontSize: '0.9375rem',
                        minHeight: 48,
                        px: 3
                      }
                    }}
                    aria-label="Category tabs"
                  >
                    {subscribedCategories.map((category, index) => {
                      const categoryLabel = getCategoryLabel(category);
                      const categoryFields = fieldsByCategory[category] || {};
                      const totalFields = Object.values(categoryFields).flat().length;
                      const mappingCount = fieldMappingCounts[category] || 0;
                      
                      return (
                        <Tab
                          key={category}
                          label={
                            <Stack direction="row" alignItems="center" spacing={1.5}>
                              <Typography variant="body2" fontWeight={500}>
                                {categoryLabel}
                              </Typography>
                              {totalFields > 0 && (
                                <Chip
                                  label={totalFields}
                                  size="small"
                                  sx={{
                                    height: 22,
                                    fontSize: '0.75rem',
                                    backgroundColor: theme.palette.action.hover,
                                    color: theme.palette.text.secondary
                                  }}
                                />
                              )}
                              {mappingCount > 0 && (
                                <Tooltip title={`${mappingCount} fields mapped`}>
                                  <Badge
                                    badgeContent={mappingCount}
                                    color="success"
                                    sx={{
                                      '& .MuiBadge-badge': {
                                        fontSize: '0.65rem',
                                        height: 16,
                                        minWidth: 16,
                                        padding: '0 4px'
                                      }
                                    }}
                                  >
                                    <Zap size={14} color={theme.palette.success.main} />
                                  </Badge>
                                </Tooltip>
                              )}
                            </Stack>
                          }
                          sx={{
                            '&.Mui-selected': {
                              color: theme.palette.primary.main
                            }
                          }}
                        />
                      );
                    })}
                  </Tabs>
                </Box>
              )}

              {/* Tab Content */}
                <PageCard>
                  {subscribedCategories.map((category, index) => {
                    const models = fieldsByCategory[category] || {};
                    const categoryLabel = getCategoryLabel(category);
                    const totalFields = Object.values(models).flat().length;
                    const hasFieldsInCategory = totalFields > 0;
                    const isVisible = activeTab === index; // Use index directly since we're mapping subscribedCategories
                    const isSupported = category === 'SCM' || category === 'TICKETING';
                    
                    if (!isVisible) return null;
                    
                    return (
                      <Box key={category}>
                        {/* Check if category supports custom fields */}
                        {!isSupported ? (
                          // Coming soon state for unsupported categories
                          <Box
                            sx={{
                              py: 8,
                              px: 2,
                              textAlign: 'center',
                              maxWidth: 500,
                              mx: 'auto'
                            }}
                          >
                            <Box
                              sx={{
                                width: 64,
                                height: 64,
                                borderRadius: '50%',
                                backgroundColor: alpha(theme.palette.info.main, 0.1),
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                mx: 'auto',
                                mb: 3
                              }}
                            >
                              <Clock size={32} color={theme.palette.info.main} />
                            </Box>
                            <Typography variant="h6" fontWeight={600} gutterBottom>
                              Coming Soon
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              Custom fields for {categoryLabel} are not available at this time. We're working on bringing this feature to more integrations.
                            </Typography>
                          </Box>
                        ) : Object.keys(models).length > 0 ? (
                          <Stack spacing={3}>
                            {/* Search Bar */}
                            <Box>
                              <TextField
                                size="medium"
                                placeholder="Search fields..."
                                value={searchQuery}
                                onChange={handleSearchChange}
                                fullWidth
                                sx={{ 
                                  '& .MuiOutlinedInput-root': {
                                    backgroundColor: theme.palette.background.paper,
                                    '& fieldset': {
                                      borderColor: theme.palette.divider,
                                    },
                                    '&:hover fieldset': {
                                      borderColor: theme.palette.divider,
                                    },
                                    '&.Mui-focused fieldset': {
                                      borderColor: theme.palette.primary.main,
                                    },
                                  },
                                }}
                                InputProps={{
                                  startAdornment: (
                                    <InputAdornment position="start">
                                      {isSearching ? (
                                        <CircularProgress size={20} />
                                      ) : (
                                        <Search size={20} color={theme.palette.text.secondary} />
                                      )}
                                    </InputAdornment>
                                  ),
                                  'aria-label': 'Search fields'
                                }}
                              />
                            </Box>
                            
                            {/* Models */}
                            <Box>
                              {Object.entries(models)
                                .sort(([a], [b]) => {
                                  // Use dataModelsByCategory order if available
                                  const categoryModels = dataModelsByCategory[category];
                                  if (categoryModels) {
                                    const orderMap = Object.fromEntries(
                                      categoryModels.map((m, idx) => [m.name, idx])
                                    );
                                    const indexA = orderMap[a] ?? 999;
                                    const indexB = orderMap[b] ?? 999;
                                    if (indexA !== indexB) return indexA - indexB;
                                  }
                                  
                                  // Fallback to default order
                                  const order = ['organization', 'repository', 'pull_request', 'branch', 'commit', 'project', 'issue', 'sprint', 'ticket', 'incident', 'change_request', 'general'];
                                  const indexA = order.indexOf(a);
                                  const indexB = order.indexOf(b);
                                  if (indexA === -1 && indexB === -1) return a.localeCompare(b);
                                  if (indexA === -1) return 1;
                                  if (indexB === -1) return -1;
                                  return indexA - indexB;
                                })
                                .map(([model, fields], index, array) => 
                                  renderModelSection(model, fields, category, index === array.length - 1)
                                )}
                            </Box>
                          </Stack>
                        ) : null}
                      </Box>
                    );
                  })}
                </PageCard>
            </Stack>
          ) : null}
        </Grid>
      </Grid>

      {/* Field Mapping Drawer - Lazy loaded */}
      <Suspense 
        fallback={
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        }
      >
        <FieldMappingDrawer
          open={drawerOpen}
          onClose={handleDrawerClose}
          onSave={handleSaveMapping}
          initialCategory={drawerCategory}
          initialModel={selectedModel}
        />
      </Suspense>

      {/* View Field Dialog */}
      {selectedField && (
        <AddFieldDialog
          open={viewDialogOpen}
          onClose={handleViewDialogClose}
          category={viewCategory}
          editField={selectedField}
          mode="view"
        />
      )}
    </>
  );
};

// Wrap with error boundary
const FieldMappingsPageWithErrorBoundary = () => (
  <FieldMappingsErrorBoundary>
    <FieldMappingsPage />
  </FieldMappingsErrorBoundary>
);

export default FieldMappingsPageWithErrorBoundary;