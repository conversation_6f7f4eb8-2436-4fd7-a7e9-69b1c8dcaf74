import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { webhookClient } from "services/webhook.service";
import { CreateWebhookPayload, UpdateWebhookPayload } from "types/webhook";
import useUserDetails from "store/user";
import { API_ENDPOINTS } from "utils/api/api-endpoints";

export const useWebhooks = () => {
  const { user } = useUserDetails();
  const organizationId = user?.organization?.id;
  const queryClient = useQueryClient();

  const useGetWebhooks = (organizationId: string) => {
    return useQuery({
      queryKey: ["webhooks", organizationId],
      queryFn: () => webhookClient.getWebhooks(),
      enabled: !!organizationId,
      staleTime: 5 * 60 * 1000, // 5 minutes
    });
  };

  const { mutateAsync: createWebhook } = useMutation({
    mutationFn: ({ payload }: { payload: Record<string, any> }) => {
      return webhookClient.createWebhook(payload);
    },
  });

  const { mutateAsync: updateWebhook } = useMutation({
    mutationFn: ({
      payload,
      id,
    }: {
      payload: Record<string, any>;
      id: string;
    }) => {
      return webhookClient.updateWebhook(id as string, payload);
    },
  });
  const {
    mutateAsync: searchWebhooks,
    isPending: isSearching,
    error: searchError,
    data: searchResult,
  } = useMutation({
    mutationFn: ({ payload }: { payload: Record<string, any> }) => {
      return webhookClient.searchWebhooks(payload);
    },
  });
  const { mutateAsync: validateWebhookEndpoint } = useMutation({
    mutationFn: (url: string) => webhookClient.validateWebhookEndpoint(url),
  });
  const { mutateAsync: deleteWebhook } = useMutation({
    mutationFn: (id: string) => {
      return webhookClient.deleteWebhook(id);
    },
  });

  return {
    attemptCreateWebhook: async (payload: Record<string, any>, cb?: any) => {
      try {
        await createWebhook({ payload });

        // Invalidate legacy list key
        queryClient.invalidateQueries({
          queryKey: ["webhooks", organizationId],
        });

        // Invalidate all search queries keyed by API_ENDPOINTS.WEBHOOKS
        queryClient.invalidateQueries({
          predicate: (q) =>
            Array.isArray(q.queryKey) &&
            q.queryKey[0] === API_ENDPOINTS.WEBHOOKS,
        });

        cb?.();
        return "Webhook created successfully";
      } catch (error: any) {
        return error?.response?.data?.message || "Failed to create webhook";
      }
    },

    attemptUpdateWebhook: async (
      payload: Record<string, any>,
      id: string,
      cb?: any
    ) => {
      try {
        await updateWebhook({ payload, id });

        // Invalidate legacy list key
        queryClient.invalidateQueries({
          queryKey: ["webhooks", organizationId],
        });

        // Invalidate all search queries keyed by API_ENDPOINTS.WEBHOOKS
        queryClient.invalidateQueries({
          predicate: (q) =>
            Array.isArray(q.queryKey) &&
            q.queryKey[0] === API_ENDPOINTS.WEBHOOKS,
        });

        cb?.();
        return "Webhook updated successfully";
      } catch (error: any) {
        return error?.response?.data?.message || "Failed to update webhook";
      }
    },

    attemptSearchWebhooks: async (payload: Record<string, any>) => {
      try {
        const res = await searchWebhooks({ payload });
        return res;
      } catch (err: any) {
        const message = err?.response?.data?.message || "Search failed";
        throw new Error(message);
      }
    },

    attemptValidateWebhookEndpoint: async (url: string) => {
      try {
        const res = await validateWebhookEndpoint(url);
        return res;
      } catch (err: any) {
        const message =
          err?.response?.data?.message || "Endpoint validation failed";
        throw new Error(message);
      }
    },

    attemptDeleteWebhook: async (id: string, cb?: any) => {
      try {
        await deleteWebhook(id);

        // Invalidate legacy list key
        queryClient.invalidateQueries({
          queryKey: ["webhooks", organizationId],
        });

        // Invalidate all search queries keyed by API_ENDPOINTS.WEBHOOKS
        queryClient.invalidateQueries({
          predicate: (q) =>
            Array.isArray(q.queryKey) &&
            q.queryKey[0] === API_ENDPOINTS.WEBHOOKS,
        });

        cb?.();
        return "Webhook deleted successfully";
      } catch (error: any) {
        return error?.response?.data?.message || "Failed to delete webhook";
      }
    },

    useGetWebhooks,
    createWebhook,
    updateWebhook,
    deleteWebhook,
  };
};
