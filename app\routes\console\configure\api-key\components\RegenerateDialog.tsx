import { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Stack,
  TextField,
  Alert,
  useTheme,
  alpha
} from '@mui/material';
import { AlertTriangle } from 'lucide-react';
import ExpirationDropdown from './ExpirationDropdown';

interface RegenerateDialogProps {
  open: boolean;
  onClose: () => void;
  onConfirm: (expirationDays: number) => void;
  isLoading?: boolean;
}

export default function RegenerateDialog({ 
  open, 
  onClose, 
  onConfirm,
  isLoading = false 
}: RegenerateDialogProps) {
  const theme = useTheme();
  const [confirmText, setConfirmText] = useState('');
  const [expirationDays, setExpirationDays] = useState(30);

  const handleConfirm = () => {
    if (confirmText.toLowerCase() === 'regenerate') {
      onConfirm(expirationDays);
      handleClose();
    }
  };

  const handleClose = () => {
    setConfirmText('');
    setExpirationDays(30);
    onClose();
  };

  const isValid = confirmText.toLowerCase() === 'regenerate';

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          p: 1
        }
      }}
    >
      <DialogTitle sx={{ pb: 1 }}>
        <Typography variant="h5" fontWeight={600}>
          Regenerate access key
        </Typography>
      </DialogTitle>

      <DialogContent sx={{ py: 3, px: 3 }}>
        <Stack spacing={3}>
          <Alert 
            severity="warning" 
            icon={<AlertTriangle size={20} />}
            sx={{ 
              backgroundColor: alpha(theme.palette.warning.main, 0.1),
              '& .MuiAlert-icon': {
                color: theme.palette.warning.main
              }
            }}
          >
            <Typography variant="body2" fontWeight={500}>
              Regenerating will permanently delete any existing access keys currently in use.
            </Typography>
          </Alert>

          <Box sx={{ mt: 4 }}>
            <ExpirationDropdown
              value={expirationDays}
              onChange={setExpirationDays}
              showLabel={true}
            />
          </Box>

          <Box sx={{ mt: 3 }}>
            <Typography variant="body1" fontWeight={600} sx={{ mb: 1.5 }}>
              Type "regenerate" below to confirm:
            </Typography>
            <TextField
              fullWidth
              value={confirmText}
              onChange={(e) => setConfirmText(e.target.value)}
              placeholder="Type 'regenerate' to confirm"
              autoComplete="off"
              error={confirmText.length > 0 && !isValid}
              helperText={
                confirmText.length > 0 && !isValid 
                  ? "Please type 'regenerate' to confirm" 
                  : ""
              }
              sx={{
                '& .MuiInputBase-input': {
                  fontFamily: 'monospace'
                }
              }}
            />
          </Box>
        </Stack>
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 2, gap: 1 }}>
        <Button 
          onClick={handleClose} 
          variant="outlined"
          sx={{ minWidth: 100 }}
          disabled={isLoading}
        >
          Cancel
        </Button>
        <Button 
          onClick={handleConfirm}
          variant="contained"
          color="error"
          disabled={!isValid || isLoading}
          sx={{ minWidth: 200 }}
        >
          Delete and regenerate key
        </Button>
      </DialogActions>
    </Dialog>
  );
}