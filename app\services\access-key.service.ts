import { API_ENDPOINTS } from "utils/api/api-endpoints";
import fetchInstance from "utils/api/fetchinstance";

export interface CreateAccessKeyPayload {
  type: 'GENERATE_REQUEST';
  expirationDateTime?: string; // UTC format: "2025-09-18T13:00:00.000Z"
}

export interface AccessKeySearchParams {
  limit?: number;
  offset?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export const accesskeyClient = {
  createAccessKey: (payload: CreateAccessKeyPayload) => {
    return fetchInstance.post(`${API_ENDPOINTS.ACCESS_KEY}s`, payload);
  },
  
  getAllAccessKeys: () => {
    return fetchInstance.get(`${API_ENDPOINTS.ACCESS_KEY}s`);
  },
  
  getAccessKeyById: (id: string) => {
    return fetchInstance.get(`${API_ENDPOINTS.ACCESS_KEY}s/${id}`);
  },
  
  deleteAccessKey: (id: string) => {
    return fetchInstance.delete(`${API_ENDPOINTS.ACCESS_KEY}s/${id}`);
  },
  
  updateAccessKey: (id: string, payload: any) => {
    return fetchInstance.patch(`${API_ENDPOINTS.ACCESS_KEY}s/${id}`, payload);
  },
};
