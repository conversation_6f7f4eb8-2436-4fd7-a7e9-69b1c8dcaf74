# API Key Architecture Documentation

## Overview

The Unizo Web Portal implements a secure API key management system that allows organizations to generate and manage access keys for API authentication. The system uses cookie-based storage with organization-specific keys and includes security features like one-time display and time-limited storage.

## Architecture Components

### 1. Main Component (`/app/routes/console/configure/api-key/index.tsx`)

The main API key management interface that provides:
- Display of existing API keys
- Generation of new API keys
- Copy functionality for API keys
- Integration with the initial setup flow

**Key Features:**
- Shows API key only once after generation
- Provides copy-to-clipboard functionality
- Responsive design with theme-aware styling
- Optional description display for onboarding context

### 2. Custom Hook (`/app/hooks/api/useAccesskey.tsx`)

Manages API key generation and cookie storage with the following functionality:

```typescript
const useAccesskey = () => {
  // Cookie management
  const cookieKey = user?.organization?.id 
    ? `access-key-${user.organization.id}` 
    : 'access-key';
  
  // 5-minute expiration for security
  const maxAge = 60 * 5; // 5 minutes
  
  // Core functions:
  - attemptToCreateAccesskey: Generates new API key
  - getAccessKey: Retrieves stored key from cookie
  - setAccessKey: Stores key in cookie
  - removeAccessKey: Removes key from cookie
}
```

**Security Features:**
- Organization-specific cookie keys prevent cross-organization access
- 5-minute cookie expiration ensures keys don't persist
- One-time display pattern for newly generated keys

### 3. API Service (`/app/services/access-key.service.ts`)

Simple service layer for API communication:

```typescript
export const AccessKeyClient = {
  createAccessKey: () => {
    return fetchInstance.post(API_ENDPOINTS.ACCESS_KEY);
  }
};
```

### 4. API Endpoints (`/app/utils/api/api-endpoints.ts`)

Defines the endpoint for access key operations:
```typescript
ACCESS_KEY: '/access-key'
```

## Data Flow

1. **Key Generation Flow:**
   ```
   User clicks "Generate API Key"
   → useAccesskey.attemptToCreateAccesskey()
   → AccessKeyClient.createAccessKey()
   → API returns new key
   → Key stored in cookie (5 min expiration)
   → Key displayed to user (one-time)
   ```

2. **Key Retrieval Flow:**
   ```
   Component mounts
   → useAccesskey.getAccessKey()
   → Check cookie for existing key
   → Display if found and not expired
   → Clear display after user copies
   ```

## Integration Points

### 1. Initial Setup Flow
- API key generation is Step 3 in the onboarding process
- Located in `/app/sections/initial-setup/index.tsx`
- Validates API key creation before allowing progression
- Shows descriptive text during onboarding

### 2. Settings Page
- Accessible via `/console/configure/api-key`
- Part of the main configuration menu
- No additional description shown in regular access

### 3. Copy Functionality
- Uses `CopyableText` component from `/@extended/Copyable-text`
- Provides visual feedback on successful copy
- Automatically reverts icon after 2 seconds

## Storage Pattern

### Cookie Structure
```
Key: access-key-{organizationId}
Value: {generated-api-key}
Expiration: 5 minutes
Path: /
```

### Why Cookies?
- Server-side readable for SSR
- Automatic expiration handling
- HttpOnly flag support (if needed)
- Organization isolation via key prefixing

## Security Considerations

1. **Time-Limited Storage:**
   - 5-minute expiration prevents long-term exposure
   - Forces regeneration for continued access

2. **One-Time Display:**
   - Key shown only immediately after generation
   - Removed from UI after copy or navigation

3. **Organization Isolation:**
   - Cookie keys include organization ID
   - Prevents cross-organization key access

4. **No Persistent Storage:**
   - Keys not stored in database after display
   - Users must copy immediately or regenerate

## Component Props and Usage

### AccessKey Component
```typescript
interface AccessKeyProps {
  showDescription?: boolean; // Show onboarding description
}

// Usage in initial setup
<AccessKey showDescription={true} />

// Usage in settings
<AccessKey />
```

### useAccesskey Hook
```typescript
const {
  isCreating,              // Loading state
  attemptToCreateAccesskey, // Generate new key
  getAccessKey,            // Retrieve from cookie
  setAccessKey,            // Store in cookie
  removeAccessKey          // Clear cookie
} = useAccesskey();
```

## UI/UX Patterns

1. **Generation State:**
   - Loading spinner during API call
   - Success toast on completion
   - Error toast on failure

2. **Display State:**
   - Blurred text field with copy button
   - "Generate API Key" button when no key exists
   - Clear visual hierarchy

3. **Copy Feedback:**
   - Icon changes from copy to checkmark
   - Tooltip shows "Copy" → "Copied"
   - 2-second feedback duration

## Error Handling

1. **API Errors:**
   - Generic error message displayed
   - Toast notification for user feedback
   - Console logging for debugging

2. **Cookie Errors:**
   - Graceful fallback to no key display
   - Automatic cleanup on expiration

## Testing Considerations

1. **Unit Tests:**
   - Mock cookie operations
   - Test expiration logic
   - Verify organization isolation

2. **Integration Tests:**
   - Test full generation flow
   - Verify copy functionality
   - Check setup flow integration

3. **E2E Tests:**
   - Complete user journey
   - Multi-organization scenarios
   - Expiration behavior

## Future Enhancement Opportunities

1. **Multiple Keys:**
   - Support for multiple API keys per organization
   - Key naming and description
   - Key rotation policies

2. **Enhanced Security:**
   - Key scoping/permissions
   - IP restrictions
   - Usage analytics

3. **Management Features:**
   - Key revocation
   - Expiration policies
   - Activity logging

4. **Developer Experience:**
   - API key testing interface
   - Documentation generation
   - SDK integration examples

## Related Components

1. **Dock Profiles** (`/app/hooks/api/dockProfiles/useDockProfile.tsx`)
   - Uses service keys for testing
   - Creates temporary test links

2. **Service Keys** (`/app/services/dock.service.ts`)
   - Related API for service-specific keys
   - Different from main API keys

## Migration Notes

When enhancing the API key system:

1. **Backward Compatibility:**
   - Maintain cookie format
   - Support existing key structure
   - Gradual migration path

2. **Database Schema:**
   - Consider storing key metadata
   - Track generation/usage history
   - Implement soft delete

3. **API Changes:**
   - Version the API endpoints
   - Add new fields incrementally
   - Maintain existing contracts

## Conclusion

The current API key implementation provides a secure, simple solution for API authentication with appropriate security measures. The architecture is well-positioned for future enhancements while maintaining a clean separation of concerns across components, hooks, and services.