import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { parseError } from "lib/utils";
import { DocksClient } from "services/dock.service";
import { serviceProfileClient } from "services/service-profile.service";
import { toast } from "sonner";
import useUserDetails from "store/user";
import { API_ENDPOINTS } from "utils/api/api-endpoints";

const DOCK_PROFILE_API = "DOCK_PROFILE_API";

const DOCK_PROFILE_S = "DOCK_PROFILE_S";

const DOCK_PROFILE_S_ALL_INTEGRATION_S = "DOCK_PROFILE_S_ALL_INTEGRATION_S";
const DOCK_PROFILE_DELETE = "DOCK_PROFILE_DELETE";

const SERVICE_KEY = "SERVICE_KEY";

export const useGetDockProfile = () => {
  const { user } = useUserDetails();
  const orgId = user?.organization?.id;

  const queryClient = useQueryClient();

  const { mutateAsync: createDockProfileMutation } = useMutation({
    mutationFn: ({ payload }: { payload: Record<string, any> }) => {
      return DocksClient.createDockProfiles(payload);
    },
  });

  const { mutateAsync: updateDockProfileMutation } = useMutation({
    mutationFn: ({
      id,
      payload,
    }: {
      id: string;
      payload: Record<string, any>;
    }) => {
      return DocksClient.updateDockProfiles(id, payload);
    },
  });

  const { mutateAsync: deleteDockProfileMutation } = useMutation({
    mutationFn: ({ id }: { id: string }) => {
      return DocksClient.deleteDockProfiles(id);
    },
  });

  const { mutateAsync: searchDockProfileMutation } = useMutation({
    mutationFn: ({ payload }: { payload: Record<string, any> }) => {
      return DocksClient.searchDockProfiles(payload);
    },
  });
  const { mutateAsync: createServiceKeyMutation } = useMutation({
    mutationFn: ({ payload }: { payload: Record<string, any> }) => {
      return DocksClient.createServiceKeys(payload);
    },
  });

  const getDockProfileById = (id: string) => {
    return useQuery({
      queryKey: [API_ENDPOINTS.DOCK_PROFILES, id],
      queryFn: async () => {
        const response = await DocksClient.getDockProfileById(id);
        return response?.data;
      },
      enabled: !!id,
    });
  };

  const getSearchServices = (payload: any) => {
    return useQuery({
      queryKey: [API_ENDPOINTS.SERVICE_KEYS, payload],
      queryFn: async () => {
        const response = await serviceProfileClient.searchServices(payload);
        return response?.data;
      },
      enabled: !!payload,
    });
  };

  return {
    attemptCreateDockProfile: async (
      payload: Record<string, any>,
      cb?: any
    ) => {
      try {
        await createDockProfileMutation({ payload } as any);

        // Invalidate queries so UI updates
        queryClient.invalidateQueries({
          queryKey: [
            API_ENDPOINTS.DOCK_PROFILES,
            DOCK_PROFILE_API,
            DOCK_PROFILE_S_ALL_INTEGRATION_S,
            DOCK_PROFILE_S,
          ],
        });

        // Refresh server-side search results
        queryClient.invalidateQueries({ queryKey: ["DOCK_PROFILE_SEARCH"] });

        cb?.();
        return "Connect UI Profile Created";
      } catch (err: any) {
        return (
          parseError(err?.response?.data)?.message ||
          "Failed to create Dock Profile"
        );
      }
    },

    attemptEditDockProfile: async (
      id: string,
      payload: Record<string, any>,
      cb?: any
    ) => {
      try {
        await updateDockProfileMutation({ id, payload });

        // Invalidate queries so UI updates
        queryClient.invalidateQueries({
          queryKey: [
            API_ENDPOINTS.DOCK_PROFILES,
            DOCK_PROFILE_API,
            DOCK_PROFILE_S_ALL_INTEGRATION_S,
            DOCK_PROFILE_S,
          ],
        });

        // Refresh server-side search results
        queryClient.invalidateQueries({ queryKey: ["DOCK_PROFILE_SEARCH"] });

        cb?.();
        return "Connect UI Profile Updated";
      } catch (err: any) {
        return (
          parseError(err?.response?.data)?.message ||
          "Failed to update Dock Profile"
        );
      }
    },

    attemptDeleteDockProfile: async (id: string, cb?: any) => {
      if (!id) {
        return "Invalid ID provided for deletion";
      }

      try {
        await deleteDockProfileMutation({ id });

        // Invalidate queries so UI updates
        queryClient.invalidateQueries({
          queryKey: [
            API_ENDPOINTS.DOCK_PROFILES,
            DOCK_PROFILE_API,
            DOCK_PROFILE_S_ALL_INTEGRATION_S,
            DOCK_PROFILE_S,
          ],
        });

        // Refresh server-side search results
        queryClient.invalidateQueries({ queryKey: ["DOCK_PROFILE_SEARCH"] });

        cb?.();
        return "Connect UI Profile Deleted";
      } catch (err: any) {
        return (
          parseError(err?.response?.data)?.message ||
          "Failed to delete Dock Profile"
        );
      }
    },

    getDockProfiles: () => {
      return useQuery({
        queryKey: [
          API_ENDPOINTS.DOCK_PROFILES,
          DOCK_PROFILE_API,
          DOCK_PROFILE_S_ALL_INTEGRATION_S,
          DOCK_PROFILE_S,
        ],
        queryFn: async () => {
          return await DocksClient.getDockProfiles();
        },
        select: (resp) => {
          return resp?.data;
        },
        enabled: !!orgId,
      });
    },

    attemptSearchDockProfile: async (
      payload: Record<string, any>,
      cb?: any
    ) => {
      try {
        const res = await searchDockProfileMutation({ payload } as any);
        cb && cb(res);
        return res;
      } catch (err: any) {
        toast.error(
          parseError(err?.response?.data)?.message || "Search failed"
        );
        throw err;
      }
    },

    attemptCreateServiceKey: async (payload: Record<string, any>, cb?: any) => {
      try {
        const data = await createServiceKeyMutation({ payload } as any);

        queryClient.invalidateQueries({
          queryKey: [API_ENDPOINTS.SERVICE_KEYS, SERVICE_KEY],
        });

        cb?.(data);

        return "Test link created successfully";
      } catch (err: any) {
        const msg = parseError(err?.response?.data)?.message;

        let errorMessage = "An unexpected error occurred.";
        if (msg?.toLowerCase().includes("service key cannot be empty")) {
          errorMessage = "Please select at least one category.";
        } else if (msg) {
          errorMessage = msg;
        }
        toast.error(errorMessage);
        return errorMessage;
      }
    },

    getSearchServices,
    getDockProfileById,
  };
};
