import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  Select,
  MenuItem,
  Checkbox,
  useTheme,
  alpha,
} from "@mui/material";
import {
  Braces,
  Brackets,
  Calendar,
  FileText,
  Hash,
  ToggleLeft,
  X,
} from "lucide-react";
import { DataModel } from "./type";
import LearnMoreLink from "components/@extended/LearnMoreLink";
import { DOMAINS } from "data/domains";
import type { AdditionalField } from "./type";

// Utility function to sanitize field names
const sanitizeFieldName = (name: string): string => {
  return name
    .trim()
    .replace(/[^a-zA-Z0-9\s]/g, "") // Remove special characters
    .replace(/\s+/g, "_") // Replace spaces with underscores
    .replace(/^_+|_+$/g, ""); // Remove leading/trailing underscores
};

interface AddFieldDialogProps {
  open: boolean;
  onClose: () => void;
  onAdd?: (field: Partial<AdditionalField>) => void;
  category: string;
  categoryLabel?: string;
  model?: string;
  dataModels?: DataModel[];
  parentField?: AdditionalField;
  editField?: AdditionalField;
  mode?: "add" | "view";
}

const AddFieldDialog = ({
  open,
  onClose,
  onAdd,
  category,
  categoryLabel,
  model,
  dataModels = [],
  parentField,
  editField,
  mode = "add",
}: AddFieldDialogProps) => {
  const theme = useTheme();
  const [fieldData, setFieldData] = useState<Partial<AdditionalField & { enableMapping?: boolean }>>({
    name: "",
    key: "",
    displayName: "",
    type: "string",
    description: "",
    required: false,
    category: category,
    model: model || dataModels[0]?.id || "",
    parentId: parentField?.id,
    enableMapping: false,
  });
  const [keyManuallyEdited, setKeyManuallyEdited] = useState(false);

  useEffect(() => {
    if (mode === "view" && editField) {
      // Handle both possible field data structures
      const fieldType = editField.dataType?.type || editField.type || "string";
      
      setFieldData({
        name: editField.name || "",
        key: editField.key || "",
        displayName: editField.displayName || "",
        type: fieldType,
        description: editField.description || "",
        required: editField.required || false,
        category: editField.category || category,
        model: editField.dataModel?.type || editField.model || model || "",
        parentId: editField.parentId || parentField?.id,
        enableMapping: false,
      });
      setKeyManuallyEdited(true); // In view mode, assume key was manually set
    } else if (mode === "add") {
      setFieldData({
        name: "",
        key: "",
        displayName: "",
        type: "string",
        description: "",
        required: false,
        category: category,
        model: model || dataModels[0]?.id || "",
        parentId: parentField?.id,
        enableMapping: false,
      });
      setKeyManuallyEdited(false);
    }
  }, [mode, editField?.id, category, model, parentField?.id]); // Reduce dependencies to prevent loops

  const handleSubmit = () => {
    if (onAdd) {
      onAdd(fieldData);
    }
    onClose();
    setFieldData({
      name: "",
      key: "",
      displayName: "",
      type: "string",
      description: "",
      required: false,
      category: category,
      model: model || dataModels[0]?.id || "",
      parentId: parentField?.id,
      enableMapping: false,
    });
    setKeyManuallyEdited(false);
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          maxWidth: 560,
        },
      }}
    >
      <DialogTitle sx={{ pb: 1 }}>
        <Stack
          direction="row"
          alignItems="flex-start"
          justifyContent="space-between"
        >
          <Box>
            <Typography variant="h5" fontWeight={600}>
              {mode === "view" ? "View Custom Field" : "Add Custom Field"}
            </Typography>
            {model && mode === "add" && !parentField && dataModels.length > 0 && (
              <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                You are adding a field to{" "}
                <Typography component="span" fontWeight={600} color="text.primary">
                  {dataModels.find(dm => dm.id === model)?.displayName || model}
                </Typography>{" "}
                in {categoryLabel || category}
              </Typography>
            )}
          </Box>
          <IconButton 
            onClick={onClose} 
            size="small" 
            sx={{ 
              mt: -0.5,
              mr: -1
            }}
          >
            <X size={20} />
          </IconButton>
        </Stack>
      </DialogTitle>
      <DialogContent sx={{ pt: 2 }}>
        <Stack spacing={3}>
          {/* Label Field */}
          <Box>
            <Typography variant="body1" fontWeight={600} sx={{ mb: 0.5 }}>
              Label
            </Typography>
            <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 1 }}>
              Human-readable name for the field
            </Typography>
            <TextField
              value={fieldData.name}
              onChange={(e) => {
                const newName = e.target.value;
                setFieldData({ 
                  ...fieldData, 
                  name: newName,
                  // Auto-generate key if not manually edited
                  key: keyManuallyEdited ? fieldData.key : sanitizeFieldName(newName).toLowerCase()
                });
              }}
              fullWidth
              placeholder="e.g., Team Size"
              size="small"
              disabled={mode === "view"}
              InputProps={{
                readOnly: mode === "view"
              }}
            />
          </Box>

          {/* Key Field */}
          <Box>
            <Typography variant="body1" fontWeight={600} sx={{ mb: 0.5 }}>
              Key
            </Typography>
            <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 1 }}>
              Unique identifier for the field (no spaces allowed)
            </Typography>
            <TextField
              value={fieldData.key}
              onChange={(e) => {
                // Force lowercase and sanitize
                const sanitizedKey = e.target.value
                  .toLowerCase()
                  .replace(/\s+/g, '_')
                  .replace(/[^a-z0-9_]/g, '');
                setFieldData({ ...fieldData, key: sanitizedKey });
                setKeyManuallyEdited(true); // User manually edited the key
              }}
              fullWidth
              placeholder="e.g., team_size"
              size="small"
              disabled={mode === "view"}
              InputProps={{
                readOnly: mode === "view"
              }}
              sx={{
                '& input': {
                  fontFamily: 'monospace',
                  fontSize: '0.875rem'
                }
              }}
            />
          </Box>

          {/* Field Type */}
          <Box>
            <Typography variant="body1" fontWeight={600} sx={{ mb: 0.5 }}>
              Field Type
            </Typography>
            <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 1 }}>
              Choose the data type for this field
            </Typography>
            <FormControl fullWidth size="small">
              <Select
                value={fieldData.type}
                onChange={(e) =>
                  setFieldData({ ...fieldData, type: e.target.value })
                }
                displayEmpty
                disabled={mode === "view"}
                readOnly={mode === "view"}
              >
                <MenuItem value="string">
                  <Stack direction="row" spacing={1} alignItems="center">
                    <FileText size={16} />
                    <Typography>String</Typography>
                  </Stack>
                </MenuItem>
                <MenuItem value="number">
                  <Stack direction="row" spacing={1} alignItems="center">
                    <Hash size={16} />
                    <Typography>Number</Typography>
                  </Stack>
                </MenuItem>
                <MenuItem value="boolean">
                  <Stack direction="row" spacing={1} alignItems="center">
                    <ToggleLeft size={16} />
                    <Typography>Boolean</Typography>
                  </Stack>
                </MenuItem>
                <MenuItem value="date">
                  <Stack direction="row" spacing={1} alignItems="center">
                    <Calendar size={16} />
                    <Typography>Date</Typography>
                  </Stack>
                </MenuItem>
                <MenuItem value="object">
                  <Stack direction="row" spacing={1} alignItems="center">
                    <Braces size={16} />
                    <Typography>Object</Typography>
                  </Stack>
                </MenuItem>
                <MenuItem value="array">
                  <Stack direction="row" spacing={1} alignItems="center">
                    <Brackets size={16} />
                    <Typography>Array</Typography>
                  </Stack>
                </MenuItem>
              </Select>
            </FormControl>
          </Box>

          {/* Checkbox for custom field mapping - Show for Ticketing in both add and view modes */}
          {(() => {
            const domain = DOMAINS.find(d => d.key === category);
            return domain?.supportsCustomAttributes ? (
              <Box>
                <Stack direction="row" alignItems="flex-start" spacing={1.5}>
                  <Checkbox 
                    size="small"
                    sx={{ p: 0, mt: 0 }}
                    checked={fieldData.enableMapping || false}
                    onChange={(e) => mode === "add" && setFieldData({ ...fieldData, enableMapping: e.target.checked })}
                    disabled={mode === "view"}
                  />
                  <Box sx={{ flex: 1 }}>
                    <Typography 
                      variant="body2" 
                      sx={{ 
                        lineHeight: 1.6,
                        cursor: mode === "add" ? 'pointer' : 'default',
                        userSelect: 'none',
                        color: 'text.secondary'
                      }}
                      onClick={() => mode === "add" && setFieldData({ ...fieldData, enableMapping: !fieldData.enableMapping })}
                    >
                      This field allows your customers to map their own custom attributes. This capability is fully supported for Jira and ServiceNow connectors. <LearnMoreLink />
                    </Typography>
                  </Box>
                </Stack>
              </Box>
            ) : null;
          })()}

          {/* Description */}
          <Box>
            <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: 0.5 }}>
              <Typography variant="body1" fontWeight={600}>
                Description
                <Typography component="span" variant="caption" color="text.secondary" sx={{ ml: 1, fontWeight: 400 }}>
                  Optional
                </Typography>
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {mode === "view" 
                  ? `${fieldData.description?.split(/\s+/).filter(word => word.length > 0).length || 0}/25 words`
                  : `${fieldData.description?.length || 0}/250`
                }
              </Typography>
            </Stack>
            <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 1 }}>
              {mode === "view" ? "Describe the purpose and usage of this field" : "Describe what this field is for"}
            </Typography>
            <TextField
              value={fieldData.description}
              onChange={(e) =>
                setFieldData({
                  ...fieldData,
                  description: e.target.value.slice(0, 250),
                })
              }
              fullWidth
              multiline
              rows={3}
              placeholder="e.g., Number of people in the team"
              disabled={mode === "view"}
              InputProps={{
                readOnly: mode === "view"
              }}
              sx={{
                '& .MuiInputBase-input': {
                  fontSize: '0.875rem'
                }
              }}
            />
          </Box>
        </Stack>
      </DialogContent>
      <DialogActions sx={{ px: 3, pb: 3 }}>
        {mode === "view" ? (
          <Button onClick={onClose} variant="contained">
            Close
          </Button>
        ) : (
          <>
            <Button onClick={onClose} variant="text">
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              variant="contained"
              disabled={!fieldData.name || !fieldData.key}
            >
              Add Field
            </Button>
          </>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default AddFieldDialog;