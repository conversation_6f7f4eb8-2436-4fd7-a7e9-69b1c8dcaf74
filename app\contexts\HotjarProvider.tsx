import React, { createContext, useContext, useEffect, useCallback, ReactNode } from 'react';
import { useLocation } from '@remix-run/react';
import { 
  initializeHotjar, 
  trackHotjarEvent, 
  identifyHotjarUser, 
  trackPageView,
  trackFunnelStep,
  trackUserFeedback,
  HOTJAR_CONFIG
} from 'utils/hotjar';
import useUserDetails from 'store/user';

interface HotjarContextType {
  isEnabled: boolean;
  trackEvent: (eventName: string, attributes?: Record<string, any>) => void;
  trackPageView: (pageName: string, pageUrl?: string) => void;
  trackFunnelStep: (funnelName: string, stepName: string, stepNumber: number, metadata?: Record<string, any>) => void;
  trackUserFeedback: (feedbackType: string, rating?: number, comment?: string) => void;
  trackUserAction: (action: string, category: string, label?: string, value?: number) => void;
  trackError: (error: Error, context?: Record<string, any>) => void;
  trackFormSubmission: (formName: string, success: boolean, metadata?: Record<string, any>) => void;
  trackSearch: (searchTerm: string, resultsCount: number, filters?: Record<string, any>) => void;
  trackEngagement: (feature: string, action: string, duration?: number) => void;
}

const HotjarContext = createContext<HotjarContextType | undefined>(undefined);

interface HotjarProviderProps {
  children: ReactNode;
}

export const HotjarProvider: React.FC<HotjarProviderProps> = ({ children }) => {
  const location = useLocation();
  const { user } = useUserDetails();

  // Initialize Hotjar on mount
  useEffect(() => {
    initializeHotjar();
  }, []);

  // Identify user when user data is available
  useEffect(() => {
    if (HOTJAR_CONFIG.enabled && user?.id) {
      identifyHotjarUser(user.id, {
        email: user.email,
        name: user.name || `${user.firstName} ${user.lastName}`.trim(),
        organization_id: user.organization?.id,
        organization_name: user.organization?.name,
        role: user.role,
        created_at: user.createdAt,
        subscription_tier: user.subscriptionTier,
        is_trial: user.isTrial,
        environment: process.env.NODE_ENV
      });
    }
  }, [user]);

  // Track page views on route change
  useEffect(() => {
    if (HOTJAR_CONFIG.enabled) {
      const pageName = getPageNameFromPath(location.pathname);
      trackPageView(pageName, location.pathname + location.search);
    }
  }, [location]);

  // Helper function to get page name from path
  const getPageNameFromPath = (path: string): string => {
    const segments = path.split('/').filter(Boolean);
    
    // Map common paths to readable names
    const pageMap: Record<string, string> = {
      'dashboard': 'Dashboard',
      'quick-start': 'Quick Start',
      'integrations': 'Integrations',
      'logs': 'API Logs',
      'setup-integrations': 'Setup Integrations',
      'profile': 'User Profile',
      'settings': 'Settings',
      'webhooks': 'Webhooks',
      'api-keys': 'API Keys',
      'environments': 'Environments',
      'billing': 'Billing',
      'support': 'Support'
    };

    // Get the main page segment (usually the first or second segment)
    const mainSegment = segments[1] || segments[0] || 'home';
    return pageMap[mainSegment] || mainSegment.charAt(0).toUpperCase() + mainSegment.slice(1);
  };

  // Track user actions with category classification
  const trackUserAction = useCallback((
    action: string, 
    category: string, 
    label?: string, 
    value?: number
  ) => {
    trackHotjarEvent(`user_action_${category}`, {
      action,
      category,
      label,
      value,
      timestamp: new Date().toISOString(),
      page_url: location.pathname
    });
  }, [location.pathname]);

  // Track errors for debugging
  const trackError = useCallback((error: Error, context?: Record<string, any>) => {
    trackHotjarEvent('error_occurred', {
      error_message: error.message,
      error_stack: error.stack,
      error_name: error.name,
      page_url: location.pathname,
      user_agent: navigator.userAgent,
      ...context
    });
  }, [location.pathname]);

  // Track form submissions
  const trackFormSubmission = useCallback((
    formName: string, 
    success: boolean, 
    metadata?: Record<string, any>
  ) => {
    trackHotjarEvent('form_submission', {
      form_name: formName,
      success,
      page_url: location.pathname,
      timestamp: new Date().toISOString(),
      ...metadata
    });
  }, [location.pathname]);

  // Track search interactions
  const trackSearch = useCallback((
    searchTerm: string, 
    resultsCount: number, 
    filters?: Record<string, any>
  ) => {
    trackHotjarEvent('search_performed', {
      search_term: searchTerm,
      results_count: resultsCount,
      has_filters: !!filters && Object.keys(filters).length > 0,
      filters,
      page_url: location.pathname,
      timestamp: new Date().toISOString()
    });
  }, [location.pathname]);

  // Track feature engagement
  const trackEngagement = useCallback((
    feature: string, 
    action: string, 
    duration?: number
  ) => {
    trackHotjarEvent('feature_engagement', {
      feature,
      action,
      duration_seconds: duration,
      page_url: location.pathname,
      timestamp: new Date().toISOString()
    });
  }, [location.pathname]);

  const value: HotjarContextType = {
    isEnabled: HOTJAR_CONFIG.enabled,
    trackEvent: trackHotjarEvent,
    trackPageView,
    trackFunnelStep,
    trackUserFeedback,
    trackUserAction,
    trackError,
    trackFormSubmission,
    trackSearch,
    trackEngagement
  };

  return (
    <HotjarContext.Provider value={value}>
      {children}
    </HotjarContext.Provider>
  );
};

// Custom hook to use Hotjar context
export const useHotjar = (): HotjarContextType => {
  const context = useContext(HotjarContext);
  if (!context) {
    throw new Error('useHotjar must be used within HotjarProvider');
  }
  return context;
};

// Convenience hooks for specific tracking scenarios
export const useTrackClick = () => {
  const { trackUserAction } = useHotjar();
  
  return useCallback((elementName: string, metadata?: Record<string, any>) => {
    trackUserAction('click', 'interaction', elementName, undefined);
    if (metadata) {
      trackHotjarEvent('element_click', { element: elementName, ...metadata });
    }
  }, [trackUserAction]);
};

export const useTrackView = () => {
  const { trackUserAction } = useHotjar();
  
  return useCallback((viewName: string, duration?: number) => {
    trackUserAction('view', 'engagement', viewName, duration);
  }, [trackUserAction]);
};

export const useTrackConversion = () => {
  const { trackFunnelStep } = useHotjar();
  
  return useCallback((conversionType: string, value?: number, metadata?: Record<string, any>) => {
    trackHotjarEvent('conversion', {
      type: conversionType,
      value,
      currency: 'USD',
      timestamp: new Date().toISOString(),
      ...metadata
    });
  }, []);
};