import React from 'react';
import {
  Box,
  Typography,
  Grid,
  useTheme,
  useMediaQuery,
  Skeleton,
} from '@mui/material';
import useUserDetails from 'store/user';

interface WelcomeHeaderProps {
  isLoading?: boolean;
}

export default function WelcomeHeader({ isLoading = false }: WelcomeHeaderProps) {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  
  // Get user data from store
  const { user } = useUserDetails();
  
  // Safely get first name with fallback
  const firstName = React.useMemo(() => {
    if (!user) return '';
    
    // Try different possible fields for first name
    const possibleFirstName = user.firstName || 
                             user.first_name || 
                             (user.name && user.name.split(' ')[0]) ||
                             user.email?.split('@')[0] ||
                             '';
                             
    // Capitalize first letter
    if (possibleFirstName) {
      return possibleFirstName.charAt(0).toUpperCase() + possibleFirstName.slice(1).toLowerCase();
    }
    
    return '';
  }, [user]);

  if (isLoading) {
    return (
      <Box sx={{ mb: 3 }}>
        <Skeleton variant="text" width={200} height={24} />
        <Skeleton variant="text" width={150} height={40} sx={{ mt: 0.5 }} />
        <Skeleton variant="text" width="100%" maxWidth={800} height={24} sx={{ mt: 2 }} />
      </Box>
    );
  }

  return (
    <Box sx={{ mb: 3 }}>
      <Grid container>
        <Grid item xs={12}>
          {/* Welcome Message */}
          <Typography
            variant="body1"
            component="p"
            sx={{
              color: theme.palette.text.primary,
              fontWeight: 500,
              mb: 1,
              fontSize: { xs: '1rem', sm: '1.125rem' },
              letterSpacing: '-0.01em',
            }}
          >
            Welcome{firstName && `, ${firstName}`}
          </Typography>

          {/* Quick Start Title */}
          <Typography
            variant={isMobile ? 'h3' : 'h1'}
            component="h1"
            sx={{
              fontWeight: 700,
              color: theme.palette.text.primary,
              mb: 2,
            }}
          >
            Quick Start
          </Typography>

          {/* Help Text */}
          <Typography
            variant="body1"
            sx={{
              color: theme.palette.text.secondary,
              maxWidth: { xs: '100%', sm: 520, md: 520 },
              lineHeight: 1.4,
              textAlign: 'justify',
              textJustify: 'inter-word',
            }}
          >
            Let's set up your account so you can start to get the most out of your integrations. 
          </Typography>
        </Grid>
      </Grid>
    </Box>
  );
}