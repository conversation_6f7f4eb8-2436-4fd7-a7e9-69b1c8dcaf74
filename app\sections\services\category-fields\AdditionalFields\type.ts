export interface DataModel {
  id: string;
  name: string;
  displayName: string;
  description: string;
  fieldCount: number;
}

export interface AdditionalField {
  id: string;
  name: string;
  key?: string;
  displayName?: string;
  type?: string;
  description?: string;
  required?: boolean;
  defaultValue?: any;
  enum?: string[];
  format?: string;
  validation?: string;
  category?: string;
  model?: string;
  createdAt?: Date;
  createdBy?: string;
  usageCount?: number;
  lastUsed?: Date;
  parentId?: string;
  children?: AdditionalField[];
  // Additional fields from API response
  dataType?: {
    type: string;
  };
  dataModel?: {
    type: string;
  };
}