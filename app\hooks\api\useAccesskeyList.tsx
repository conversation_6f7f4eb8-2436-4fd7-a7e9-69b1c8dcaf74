import { useQuery } from "@tanstack/react-query";
import { accesskeyClient } from "services/access-key.service";
import { API_ENDPOINTS } from "utils/api/api-endpoints";

export interface AccessKey {
  id: string;
  key?: string;
  createdAt: string;
  expirationDateTime?: string;
  type: string;
  status?: string;
}

export const useAccesskeyList = () => {
  const {
    data,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: [API_ENDPOINTS.ACCESS_KEY, 'list'],
    queryFn: async () => {
      const response = await accesskeyClient.getAllAccessKeys({
        limit: 1,
        offset: 0,
        sortBy: 'createdAt',
        sortOrder: 'desc'
      });
      return response?.data?.data || [];
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const firstKey = data?.[0] as AccessKey | undefined;

  return {
    accessKey: firstKey,
    isLoading,
    error,
    refetch,
    hasKey: !!firstKey
  };
};