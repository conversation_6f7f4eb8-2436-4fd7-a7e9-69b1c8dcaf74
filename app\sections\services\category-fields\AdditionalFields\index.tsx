import React, { useState, useEffect } from "react";
import {
   <PERSON>,
   Card,
   CardContent,
   <PERSON><PERSON>,
   <PERSON><PERSON><PERSON>,
   Button,
   IconButton,
   Chip,
   Grid,
   Table,
   TableBody,
   TableCell,
   TableContainer,
   TableHead,
   TableRow,
   TextField,
   Dialog,
   DialogTitle,
   DialogContent,
   DialogActions,
   FormControl,
   FormHelperText,
   InputLabel,
   Select,
   MenuItem,
   Tooltip,
   useTheme,
   alpha,
   Collapse,
   Checkbox,
} from "@mui/material";
import { useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import {
   PlusOutlined,
   DeleteOutlined,
   CloseOutlined,
   SaveOutlined,
   DownOutlined,
   RightOutlined,
} from "@ant-design/icons";
import {
   Braces,
   Brackets,
   Calendar,
   FileText,
   Hash,
   ToggleLeft,
   X,
   Eye,
} from "lucide-react";
import { DataModel } from "./type";
import { dataModelsByCategory } from "./data-sets";
import { getDataModelByCategory } from "./utils";
import FieldList from "./field-list";
import useAdditionalAttributes from "../hooks/use-additional-fiels";
import { State } from "hooks/useStatus";
import type { AdditionalFields } from "types/additional-attributes";
import LearnMoreLink from "components/@extended/LearnMoreLink";
import { DOMAINS } from "data/domains";

interface AdditionalField {
   id: string;
   name: string;
   key?: string;
   displayName: string;
   type: string;
   description: string;
   required: boolean;
   defaultValue?: any;
   enum?: string[];
   format?: string;
   validation?: string;
   category: string;
   model: string;
   createdAt: Date;
   createdBy: string;
   usageCount: number;
   lastUsed?: Date;
   parentId?: string;
   children?: AdditionalField[];
}

const mockFields: AdditionalField[] = [
   // Organization fields
   {
      id: "1",
      name: "compliance_tier",
      displayName: "Compliance Tier",
      type: "string",
      description: "Organization compliance tier level",
      required: true,
      enum: ["TIER_1", "TIER_2", "TIER_3"],
      category: "SCM",
      model: "organization",
      createdAt: new Date("2024-01-10"),
      createdBy: "John Doe",
      usageCount: 45,
      lastUsed: new Date("2024-01-20"),
   },
   {
      id: "2",
      name: "team_size",
      displayName: "Team Size",
      type: "number",
      description: "Number of team members in the organization",
      required: false,
      category: "SCM",
      model: "organization",
      createdAt: new Date("2024-01-12"),
      createdBy: "Jane Smith",
      usageCount: 38,
      lastUsed: new Date("2024-01-19"),
   },
   // Repository fields
   {
      id: "3",
      name: "project_owner",
      displayName: "Project Owner",
      type: "string",
      description: "The owner or lead maintainer of the repository",
      required: true,
      category: "SCM",
      model: "repository",
      createdAt: new Date("2024-01-10"),
      createdBy: "John Doe",
      usageCount: 42,
      lastUsed: new Date("2024-01-20"),
   },
   {
      id: "4",
      name: "compliance_level",
      displayName: "Compliance Level",
      type: "string",
      description: "Security compliance level for the repository",
      required: false,
      enum: ["HIGH", "MEDIUM", "LOW"],
      defaultValue: "MEDIUM",
      category: "SCM",
      model: "repository",
      createdAt: new Date("2024-01-12"),
      createdBy: "Jane Smith",
      usageCount: 35,
      lastUsed: new Date("2024-01-19"),
   },
   {
      id: "5",
      name: "last_security_scan",
      displayName: "Last Security Scan",
      type: "date",
      description: "Date of the last security vulnerability scan",
      required: false,
      format: "date-time",
      category: "SCM",
      model: "repository",
      createdAt: new Date("2024-01-08"),
      createdBy: "Security Team",
      usageCount: 22,
      lastUsed: new Date("2024-01-18"),
   },
   // Branch fields
   {
      id: "6",
      name: "protected_by",
      displayName: "Protected By",
      type: "array",
      description: "List of users who can approve changes",
      required: false,
      category: "SCM",
      model: "branch",
      createdAt: new Date("2024-01-05"),
      createdBy: "Admin",
      usageCount: 15,
      lastUsed: new Date("2024-01-15"),
   },
   // Pull Request fields
   {
      id: "7",
      name: "review_priority",
      displayName: "Review Priority",
      type: "string",
      description: "Priority level for code review",
      required: false,
      enum: ["CRITICAL", "HIGH", "NORMAL", "LOW"],
      category: "SCM",
      model: "pull_request",
      createdAt: new Date("2024-01-15"),
      createdBy: "Dev Team",
      usageCount: 28,
      lastUsed: new Date("2024-01-20"),
   },
   {
      id: "8",
      name: "estimated_impact",
      displayName: "Estimated Impact",
      type: "string",
      description: "Estimated impact of the changes",
      required: false,
      category: "SCM",
      model: "pull_request",
      createdAt: new Date("2024-01-16"),
      createdBy: "Dev Team",
      usageCount: 20,
      lastUsed: new Date("2024-01-19"),
   },
   // Example object field with children
   {
      id: "9",
      name: "organization_details",
      displayName: "Organization Details",
      type: "object",
      description: "Detailed organization information",
      required: false,
      category: "SCM",
      model: "organization",
      createdAt: new Date("2024-01-17"),
      createdBy: "Admin",
      usageCount: 15,
      lastUsed: new Date("2024-01-20"),
      children: [
         {
            id: "10",
            name: "address",
            displayName: "Address",
            type: "string",
            description: "Organization address",
            required: false,
            category: "SCM",
            model: "organization",
            parentId: "9",
            createdAt: new Date("2024-01-17"),
            createdBy: "Admin",
            usageCount: 10,
         },
         {
            id: "11",
            name: "contact_info",
            displayName: "Contact Information",
            type: "object",
            description: "Contact details",
            required: false,
            category: "SCM",
            model: "organization",
            parentId: "9",
            createdAt: new Date("2024-01-17"),
            createdBy: "Admin",
            usageCount: 8,
            children: [
               {
                  id: "12",
                  name: "email",
                  displayName: "Email",
                  type: "string",
                  description: "Contact email",
                  required: true,
                  category: "SCM",
                  model: "organization",
                  parentId: "11",
                  createdAt: new Date("2024-01-17"),
                  createdBy: "Admin",
                  usageCount: 5,
               },
            ],
         },
      ],
   },
   // Example array field
   {
      id: "13",
      name: "team_members",
      displayName: "Team Members",
      type: "array",
      description: "List of team members",
      required: false,
      category: "SCM",
      model: "organization",
      createdAt: new Date("2024-01-18"),
      createdBy: "HR",
      usageCount: 25,
      lastUsed: new Date("2024-01-20"),
      children: [],
   },
];

interface AddFieldDialogProps {
   open: boolean;
   onClose: () => void;
   onAdd: (field: Partial<AdditionalField>) => void;
   category: string;
   categoryLabel?: string;
   model?: string;
   dataModels: DataModel[];
   parentField?: AdditionalField;
   editField?: AdditionalField;
   mode?: "add" | "view";
}

const AddFieldDialog = ({
   open,
   onClose,
   onAdd,
   category,
   categoryLabel,
   model,
   dataModels,
   parentField,
   editField,
   mode = "add",
}: AddFieldDialogProps) => {
   const theme = useTheme();
   const [fieldData, setFieldData] = useState<Partial<AdditionalField & { enableMapping?: boolean }>>({
      name: "",
      key: "",
      displayName: "",
      type: "string",
      description: "",
      required: false,
      category: category,
      model: model || dataModels[0]?.id || "",
      parentId: parentField?.id,
      enableMapping: false,
   });
   const [keyManuallyEdited, setKeyManuallyEdited] = useState(false);

   useEffect(() => {
      if (mode === "view" && editField) {
         setFieldData({
            ...editField,
            parentId: editField.parentId || parentField?.id,
         });
         setKeyManuallyEdited(true); // In view mode, assume key was manually set
      } else {
         setFieldData({
            name: "",
            key: "",
            displayName: "",
            type: "string",
            description: "",
            required: false,
            category: category,
            model: model || dataModels[0]?.id || "",
            parentId: parentField?.id,
            enableMapping: false,
         });
         setKeyManuallyEdited(false);
      }
   }, [mode, editField, category, model, dataModels, parentField]);

   const handleSubmit = () => {
      onAdd(fieldData);
      onClose();
      setFieldData({
         name: "",
         key: "",
         displayName: "",
         type: "string",
         description: "",
         required: false,
         category: category,
         model: model || dataModels[0]?.id || "",
         parentId: parentField?.id,
         enableMapping: false,
      });
      setKeyManuallyEdited(false);
   };

   return (
      <Dialog
         open={open}
         onClose={onClose}
         maxWidth="sm"
         fullWidth
         PaperProps={{
            sx: {
               borderRadius: 2,
               maxWidth: 560,
            },
         }}
      >
         <DialogTitle sx={{ pb: 1 }}>
            <Stack
               direction="row"
               alignItems="flex-start"
               justifyContent="space-between"
            >
               <Box>
                  <Typography variant="h5" fontWeight={600}>
                     {mode === "view" ? "View Custom Field" : "Add Custom Field"}
                  </Typography>
                  {model && mode === "add" && !parentField && (
                     <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                        You are adding a field to{" "}
                        <Typography component="span" fontWeight={600} color="text.primary">
                           {dataModels.find(dm => dm.id === model)?.displayName || model}
                        </Typography>{" "}
                        in {categoryLabel || category}
                     </Typography>
                  )}
               </Box>
               <IconButton 
                  onClick={onClose} 
                  size="small" 
                  sx={{ 
                     mt: -0.5,
                     mr: -1
                  }}
               >
                  <X size={20} />
               </IconButton>
            </Stack>
         </DialogTitle>
         <DialogContent sx={{ pt: 2 }}>
            <Stack spacing={3}>
               {/* Label Field */}
               <Box>
                  <Typography variant="body1" fontWeight={600} sx={{ mb: 0.5 }}>
                     Label
                  </Typography>
                  <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 1 }}>
                     Human-readable name for the field
                  </Typography>
                  <TextField
                     value={fieldData.name}
                     onChange={(e) => {
                        const newName = e.target.value;
                        setFieldData({ 
                           ...fieldData, 
                           name: newName,
                           // Auto-generate key if not manually edited
                           key: keyManuallyEdited ? fieldData.key : sanitizeFieldName(newName).toLowerCase()
                        });
                     }}
                     fullWidth
                     placeholder="e.g., Team Size"
                     size="small"
                     disabled={mode === "view"}
                     InputProps={{
                        readOnly: mode === "view"
                     }}
                  />
               </Box>

               {/* Key Field */}
               <Box>
                  <Typography variant="body1" fontWeight={600} sx={{ mb: 0.5 }}>
                     Key
                  </Typography>
                  <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 1 }}>
                     Unique identifier for the field (no spaces allowed)
                  </Typography>
                  <TextField
                     value={fieldData.key}
                     onChange={(e) => {
                        // Force lowercase and sanitize
                        const sanitizedKey = e.target.value
                           .toLowerCase()
                           .replace(/\s+/g, '_')
                           .replace(/[^a-z0-9_]/g, '');
                        setFieldData({ ...fieldData, key: sanitizedKey });
                        setKeyManuallyEdited(true); // User manually edited the key
                     }}
                     fullWidth
                     placeholder="e.g., team_size"
                     size="small"
                     disabled={mode === "view"}
                     InputProps={{
                        readOnly: mode === "view"
                     }}
                     sx={{
                        '& input': {
                           fontFamily: 'monospace',
                           fontSize: '0.875rem'
                        }
                     }}
                  />
               </Box>

               {/* Field Type */}
               <Box>
                  <Typography variant="body1" fontWeight={600} sx={{ mb: 0.5 }}>
                     Field Type
                  </Typography>
                  <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 1 }}>
                     Choose the data type for this field
                  </Typography>
                  <FormControl fullWidth size="small">
                     <Select
                        value={fieldData.type}
                        onChange={(e) =>
                           setFieldData({ ...fieldData, type: e.target.value })
                        }
                        displayEmpty
                        disabled={mode === "view"}
                        readOnly={mode === "view"}
                     >
                        <MenuItem value="string">
                           <Stack direction="row" spacing={1} alignItems="center">
                              <FileText size={14} color={theme.palette.success.main} />
                              <span>String</span>
                           </Stack>
                        </MenuItem>
                        <MenuItem value="number">
                           <Stack direction="row" spacing={1} alignItems="center">
                              <Hash size={14} color={theme.palette.info.main} />
                              <span>Number</span>
                           </Stack>
                        </MenuItem>
                        <MenuItem value="boolean">
                           <Stack direction="row" spacing={1} alignItems="center">
                              <ToggleLeft size={14} color={theme.palette.warning.main} />
                              <span>Boolean</span>
                           </Stack>
                        </MenuItem>
                        <MenuItem value="date">
                           <Stack direction="row" spacing={1} alignItems="center">
                              <Calendar size={14} color={theme.palette.secondary.main} />
                              <span>Date</span>
                           </Stack>
                        </MenuItem>
                        <MenuItem value="array">
                           <Stack direction="row" spacing={1} alignItems="center">
                              <Brackets size={14} color={theme.palette.error.light} />
                              <span>Array</span>
                           </Stack>
                        </MenuItem>
                        <MenuItem value="object">
                           <Stack direction="row" spacing={1} alignItems="center">
                              <Braces size={14} color={theme.palette.primary.dark} />
                              <span>Object</span>
                           </Stack>
                        </MenuItem>
                     </Select>
                  </FormControl>
               </Box>
               
               {/* Custom Field Mapping Option - Only for categories that support custom attributes */}
               {(() => {
                  const domain = DOMAINS.find(d => d.key === category);
                  return domain?.supportsCustomAttributes ? (
                     <Box>
                        <Stack direction="row" alignItems="flex-start" spacing={1}>
                           <Checkbox 
                              size="small"
                              sx={{ p: 0, mt: -0.5 }}
                              checked={fieldData.enableMapping || false}
                              onChange={(e) => setFieldData({ ...fieldData, enableMapping: e.target.checked })}
                              disabled={mode === "view"}
                           />
                           <Box flex={1}>
                              <Typography 
                                 component="p" 
                                 variant="body2" 
                                 sx={{ 
                                    lineHeight: 1.5, 
                                    color: 'text.secondary',
                                    m: 0 
                                 }}
                              >
                                 This field allows your customers to map their own custom attributes. This capability is fully supported for Jira and ServiceNow connectors.{' '}
                                 <LearnMoreLink 
                                    href="https://docs.unizo.ai/docs/unizo-console/schema-studio"
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    sx={{ fontSize: 'inherit' }}
                                 >
                                    Learn more
                                 </LearnMoreLink>
                              </Typography>
                           </Box>
                        </Stack>
                     </Box>
                  ) : null;
               })()}
               
               {/* Description Field */}
               <Box>
                  <Typography variant="body1" fontWeight={600} sx={{ mb: 0.5 }}>
                     Description
                     <Typography component="span" variant="caption" color="text.secondary" sx={{ ml: 1, fontWeight: 400 }}>
                        Optional
                     </Typography>
                  </Typography>
                  <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 1 }}>
                     Describe the purpose and usage of this field
                  </Typography>
                  <TextField
                     value={fieldData.description}
                     onChange={(e) => {
                        const text = e.target.value;
                        const wordCount = getWordCount(text);
                        // Allow typing if under limit or if deleting text
                        if (wordCount <= 25 || text.length < (fieldData.description || '').length) {
                           setFieldData({ ...fieldData, description: text });
                        }
                     }}
                     fullWidth
                     multiline
                     rows={2}
                     size="small"
                     placeholder="e.g., Stores the number of team members in the organization"
                     disabled={mode === "view"}
                     InputProps={{
                        readOnly: mode === "view"
                     }}
                     sx={{
                        '& .MuiOutlinedInput-root': {
                           fontSize: '0.875rem'
                        }
                     }}
                  />
                  <Typography 
                     variant="caption" 
                     color={getWordCount(fieldData.description || '') > 25 ? "error" : "text.secondary"}
                     sx={{ mt: 0.5, display: 'block' }}
                  >
                     {getWordCount(fieldData.description || '')}/25 words
                  </Typography>
               </Box>
               {/* <FormControlLabel
            control={
              <Switch
                checked={fieldData.required}
                onChange={(e) => setFieldData({ ...fieldData, required: e.target.checked })}
              />
            }
            label="Required Field"
          /> */}
            </Stack>
         </DialogContent>
         <DialogActions sx={{ px: 3, pb: 2 }}>
            {mode === "view" ? (
               <Button onClick={onClose} variant="contained">
                  Close
               </Button>
            ) : (
               <>
                  <Button onClick={onClose}>Cancel</Button>
                  <Button
                     variant="contained"
                     onClick={handleSubmit}
                     disabled={!fieldData.name || !fieldData.key}
                  >
                     Add Field
                  </Button>
               </>
            )}
         </DialogActions>
      </Dialog>
   );
};

export function sanitizeFieldName(input: string): string {
  return input
    .toLowerCase() // Convert everything to lowercase first
    .replace(/\s+/g, "_") // Replace spaces with underscores
    .replace(/[^a-z0-9_]/g, ""); // Remove any non-alphanumeric characters except underscores
}

function getWordCount(text: string): number {
  if (!text || !text.trim()) return 0;
  return text.trim().split(/\s+/).filter(word => word.length > 0).length;
}

export default function AdditionalFields({
   category,
   categoryLabel,
   initialModel,
}: {
   category: string;
   categoryLabel: string;
   initialModel?: string | null;
}) {
   const [fields, setFields] = useState<any[]>([]);
   const [addDialogOpen, setAddDialogOpen] = useState(false);
   const [selectedModel, setSelectedModel] = useState<string | null>(null);
   const [expandedModels, setExpandedModels] = useState<string[]>([]);
   const [parentField, setParentField] = useState<AdditionalField | undefined>(
      undefined
   );
   const [expandedFields, setExpandedFields] = useState<string[]>([]);
   const [editField, setEditField] = useState<AdditionalField | undefined>(
      undefined
   );
   const [dialogMode, setDialogMode] = useState<"add" | "view">("add");
   const [deleteConfirmDialog, setDeleteConfirmDialog] = useState<{
      open: boolean;
      field?: AdditionalField;
   }>({ open: false });
   const [highlightedModel, setHighlightedModel] = useState<string | null>(null);
   const theme = useTheme();
   const queryClient = useQueryClient();

   const { actions, additionalAttributes = [] } = useAdditionalAttributes()

   const dataModels = getDataModelByCategory(category);

   // Expand initial model when provided, scroll to it and highlight it
   useEffect(() => {
      if (initialModel && !expandedModels.includes(initialModel)) {
         setExpandedModels(prev => [...prev, initialModel]);
         setHighlightedModel(initialModel);
         // Scroll to model after a short delay to ensure DOM is updated
         setTimeout(() => {
            const element = document.getElementById(`model-section-${initialModel}`);
            if (element) {
               element.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }
         }, 300);
         // Remove highlight after a few seconds
         setTimeout(() => {
            setHighlightedModel(null);
         }, 3000);
      }
   }, [initialModel]);

   useEffect(() => {
      if (additionalAttributes?.length) {
         // Restrict attributes to the currently selected category
         const attributesForCategory = additionalAttributes.filter(
            (attr: any) => attr?.category?.type === category
         );

         const transform = (attributes: AdditionalFields.Root[], parentId: string | null) => {
            const transformedData: Array<AdditionalField> = attributes?.map((i) => {
               return {
                  id: i.id,
                  type: i?.dataType?.type,
                  name: i?.name,
                  key: i?.key || i?.name,
                  displayName: i?.name,
                  description: i?.description,
                  required: false,
                  model: i?.dataModel?.type,
                  createdAt: new Date(i?.changeLog?.createdDateTime),
                  createdBy: 'Current User',
                  usageCount: 0,
                  parentId: '',
                  children: transform(i?.children || [], i.id)
               } as AdditionalField
            })
            return transformedData;
         }
         setFields(transform(attributesForCategory, null))
      } else {
         setFields([])
      }

   }, [additionalAttributes, category])

   const handleAddField = (fieldData: Partial<AdditionalField>) => {

      const payload = {
         "type": "PREDEFINED",
         "name": fieldData.name as string,
         "key": fieldData.key as string,
         "description": fieldData.description as string,
         "state": State.ACTIVE,
         "category": {
            "type": category
         },
         "dataModel": {
            "type": fieldData.model!
         },
         "dataType": {
            "type": fieldData?.type as string,
         },
         "children": [],
      } as AdditionalFields.CreatePayload

      if (fieldData.parentId) {
         payload.parent = { id: fieldData.parentId }
      }

      actions
         .create({ payload })
         .then(() => {
            console.log('field created successfully')
            // Trigger a refetch to get the latest fields including the newly created one
            // This ensures Step 3 will have access to all fields
            queryClient.invalidateQueries({ queryKey: ['additional-attributes'] })
            // toast.success('Custom field created successfully')
         })
         .catch((error) => {
            console.error('Failed to create field:', error)
            toast.error('Failed to create field')
         })
   };


   const handleDeleteField = (fieldToDelete: AdditionalField) => {
      actions.delete(fieldToDelete.id).then(() => {
         setDeleteConfirmDialog({ open: false });
      }).catch(() => console.log('failed to delete'))
   };

   const openViewDialog = (field: AdditionalField) => {
      setEditField(field);
      setDialogMode("view");
      setSelectedModel(field.model);
      setAddDialogOpen(true);
   };

   const openAddDialog = (model?: string, parent?: AdditionalField) => {
      setEditField(undefined);
      setDialogMode("add");
      setSelectedModel(model || null);
      setParentField(parent);
      setAddDialogOpen(true);
   };

   const toggleModelExpansion = (modelId: string) => {
      setExpandedModels((prev) =>
         prev.includes(modelId)
            ? prev.filter((id) => id !== modelId)
            : [...prev, modelId]
      );
   };

   const toggleFieldExpansion = (fieldId: string) => {
      setExpandedFields((prev) =>
         prev.includes(fieldId)
            ? prev.filter((id) => id !== fieldId)
            : [...prev, fieldId]
      );
   };

   const openAddChildDialog = (field: AdditionalField) => {
      openAddDialog(field.model, field);
   };

   const getFieldsByModel = (modelId: string) => {
      return fields.filter((field) => field.model === modelId);
   };

   const getTypeColor = (type: string) => {
      const colors: Record<string, string> = {
         string: theme.palette.info.main,
         number: theme.palette.success.main,
         boolean: theme.palette.warning.main,
         date: theme.palette.primary.main,
         array: theme.palette.secondary.main,
         object: theme.palette.error.main,
      };
      return colors[type] || theme.palette.grey[500];
   };

   // Recursive component to render field rows with children
   const FieldRow: React.FC<{ field: AdditionalField; level?: number }> = ({
      field,
      level = 0,
   }) => {
      const hasChildren = field.children && field.children.length > 0;
      const canHaveChildren = field.type === "object" || field.type === "array";
      const isExpanded = expandedFields.includes(field.id);

      return (
         <>
            <TableRow
               key={field.id}
               sx={{
                  "&:hover": {
                     bgcolor: alpha(theme.palette.action.hover, 0.04),
                  },
               }}
            >
               <TableCell>
                  <Stack
                     direction="row"
                     spacing={1}
                     alignItems="flex-start"
                     sx={{ pl: level * 4 }}
                  >
                     {canHaveChildren && (
                        <IconButton
                           size="small"
                           onClick={() => toggleFieldExpansion(field.id)}
                           sx={{ p: 0.25, mt: -0.5 }}
                        >
                           {isExpanded ? <DownOutlined /> : <RightOutlined />}
                        </IconButton>
                     )}
                     <Box>
                        <Typography variant="body2" fontFamily="monospace">
                           {field.name}
                        </Typography>
                        {field.description && (
                           <Typography
                              variant="caption"
                              color="text.secondary"
                              sx={{
                                 display: 'block',
                                 mt: 0.5,
                                 wordBreak: 'break-word',
                                 whiteSpace: 'normal',
                              }}
                           >
                              {field.description}
                           </Typography>
                        )}
                     </Box>
                  </Stack>
               </TableCell>
               <TableCell>
                  <Chip
                     label={field.type}
                     size="small"
                     sx={{
                        bgcolor: alpha(getTypeColor(field.type), 0.1),
                        color: getTypeColor(field.type),
                        fontWeight: 500,
                     }}
                  />
               </TableCell>
               <TableCell>
                  <Stack direction="row" spacing={1}>
                     {canHaveChildren && (
                        <Tooltip title="Add child field">
                           <IconButton
                              size="small"
                              onClick={() => openAddChildDialog(field)}
                              sx={{
                                 border: "none",
                                 color: "primary.main",
                                 "&:hover": {
                                    bgcolor: alpha(theme.palette.primary.main, 0.1),
                                 },
                              }}
                           >
                              <PlusOutlined />
                           </IconButton>
                        </Tooltip>
                     )}
                     <Tooltip title="View field">
                        <IconButton
                           size="small"
                           onClick={() => openViewDialog(field)}
                           sx={{
                              border: "none",
                              "&:hover": {
                                 bgcolor: alpha(theme.palette.primary.main, 0.1),
                                 color: "primary.main",
                              },
                           }}
                        >
                           <Eye size={16} />
                        </IconButton>
                     </Tooltip>
                     {/* <Tooltip title="Duplicate field">
                <IconButton
                  size="small"
                  sx={{
                    border: 'none',
                    '&:hover': {
                      bgcolor: alpha(theme.palette.info.main, 0.1),
                      color: 'info.main'
                    }
                  }}
                >
                  <CopyOutlined />
                </IconButton>
              </Tooltip> */}
                     <Tooltip title="Delete field">
                        <IconButton
                           size="small"
                           onClick={() => setDeleteConfirmDialog({ open: true, field })}
                           sx={{
                              border: "none",
                              color: "error.main",
                              "&:hover": {
                                 bgcolor: alpha(theme.palette.error.main, 0.1),
                              },
                           }}
                        >
                           <DeleteOutlined />
                        </IconButton>
                     </Tooltip>
                  </Stack>
               </TableCell>
            </TableRow>
            {isExpanded &&
               hasChildren &&
               field.children?.map((child) => (
                  <FieldRow key={child.id} field={child} level={level + 1} />
               ))}
         </>
      );
   };

   return (
      <Box sx={{ height: "100%", overflow: "auto" }}>
         {/* Data Models with Fields */}
         <Stack spacing={2}>

            {dataModels.map((model) => {
               const modelFields = getFieldsByModel(model.id);
               const isExpanded = expandedModels.includes(model.id);

               return (
                  <Card
                     key={model.id}
                     id={`model-section-${model.id}`}
                     variant="outlined"
                     sx={{
                        boxShadow: "none",
                        border: `1px solid ${highlightedModel === model.id ? theme.palette.primary.main : theme.palette.mode === "dark" ? "rgba(255, 255, 255, 0.12)" : "#e5e5e5"}`,
                        bgcolor: highlightedModel === model.id ? alpha(theme.palette.primary.main, 0.04) : 'transparent',
                        transition: "all 0.3s ease",
                        animation: highlightedModel === model.id ? 'pulse 1s ease-in-out 2' : 'none',
                        '@keyframes pulse': {
                           '0%': {
                              boxShadow: `0 0 0 0 ${alpha(theme.palette.primary.main, 0.4)}`
                           },
                           '50%': {
                              boxShadow: `0 0 0 8px ${alpha(theme.palette.primary.main, 0)}`
                           },
                           '100%': {
                              boxShadow: `0 0 0 0 ${alpha(theme.palette.primary.main, 0)}`
                           }
                        },
                        "&:hover": {
                           borderColor: "primary.main",
                           bgcolor: alpha(theme.palette.primary.main, 0.02),
                        },
                     }}
                  >
                     <CardContent
                        sx={{ pb: isExpanded && modelFields.length > 0 ? 1 : 2 }}
                     >
                        {/* Model Header */}
                        <Stack
                           direction="row"
                           alignItems="center"
                           justifyContent="space-between"
                           sx={{
                              cursor: "pointer",
                              py: 0.5,
                           }}
                           onClick={() => toggleModelExpansion(model.id)}
                        >
                           <Stack direction="row" alignItems="center" spacing={2}>
                              <IconButton
                                 size="small"
                                 sx={{
                                    border: "none",
                                    transition: "all 0.2s ease",
                                    "&:hover": {
                                       bgcolor: alpha(theme.palette.action.hover, 0.1),
                                       transform: "scale(1.1)",
                                    },
                                 }}
                              >
                                 {isExpanded ? <DownOutlined /> : <RightOutlined />}
                              </IconButton>
                              <Stack>
                                 <Typography variant="h6" fontWeight={600}>
                                    {model.displayName}
                                 </Typography>
                                 <Typography variant="caption" color="text.secondary">
                                    {model.description}
                                 </Typography>
                              </Stack>
                           </Stack>
                           <Stack direction="row" alignItems="center" spacing={1} sx={{ flexShrink: 0, whiteSpace: 'nowrap' }}>
                              <Typography 
                                 variant="body2" 
                                 color="text.secondary"
                                 sx={{ 
                                    fontWeight: 500,
                                    fontSize: '0.875rem',
                                    whiteSpace: 'nowrap'
                                 }}
                              >
                                 {modelFields.length} {modelFields.length === 1 ? 'Custom Unified Field' : 'Custom Unified Fields'}
                              </Typography>
                              <Box sx={{ width: 1, height: 16, borderLeft: `1px solid ${theme.palette.divider}` }} />
                              <Button
                                 size="small"
                                 variant="outlined"
                                 startIcon={<PlusOutlined />}
                                 onClick={(e) => {
                                    e.stopPropagation();
                                    openAddDialog(model.id);
                                 }}
                                 sx={{
                                    borderRadius: 1,
                                    textTransform: 'none',
                                    fontWeight: 500,
                                    minWidth: 'auto',
                                    px: 1.5,
                                    py: 0.5,
                                    borderColor: theme.palette.primary.main,
                                    color: theme.palette.primary.main,
                                    "&:hover": {
                                       borderColor: theme.palette.primary.dark,
                                       bgcolor: alpha(theme.palette.primary.main, 0.08),
                                    },
                                 }}
                              >
                                 Add Field
                              </Button>
                           </Stack>
                        </Stack>

                        {/* Model Fields Table */}
                        <Collapse in={isExpanded}>
                           <Box sx={{ 
                              mt: 2,
                              mx: -2,
                              mb: -2,
                              px: 2,
                              pb: 2,
                              backgroundColor: theme.palette.mode === 'dark' 
                                 ? alpha(theme.palette.background.default, 0.4)
                                 : theme.palette.grey[50],
                              borderBottomLeftRadius: 4,
                              borderBottomRightRadius: 4,
                           }}>
                              <FieldList
                                 modelFields={modelFields}
                                 toggleFieldExpansion={toggleFieldExpansion}
                                 setDeleteConfirmDialog={setDeleteConfirmDialog}
                                 openAddChildDialog={openAddChildDialog}
                                 openViewDialog={openViewDialog}
                                 expandedFields={expandedFields}
                              />
                           </Box>
                        </Collapse>
                     </CardContent>
                  </Card>
               );
            })}
         </Stack>

         {/* Add/Edit Field Dialog */}
         <AddFieldDialog
            open={addDialogOpen}
            onClose={() => {
               setAddDialogOpen(false);
               setSelectedModel(null);
               setParentField(undefined);
               setEditField(undefined);
               setDialogMode("add");
            }}
            onAdd={handleAddField}
            category={category}
            categoryLabel={categoryLabel}
            model={selectedModel || undefined}
            dataModels={dataModels}
            parentField={parentField}
            editField={editField}
            mode={dialogMode}
         />

         {/* Delete Confirmation Dialog */}
         <Dialog
            open={deleteConfirmDialog.open}
            onClose={() => setDeleteConfirmDialog({ open: false })}
            maxWidth="xs"
            fullWidth
         >
            <DialogTitle>Delete Field</DialogTitle>
            <DialogContent>
               <Typography>
                  Are you sure you want to delete the field "
                  {deleteConfirmDialog.field?.name}"?
                  {deleteConfirmDialog.field?.children &&
                     deleteConfirmDialog.field.children.length > 0 &&
                     ` This will also delete ${deleteConfirmDialog.field.children.length} child field(s).`}
               </Typography>
            </DialogContent>
            <DialogActions>
               <Button onClick={() => setDeleteConfirmDialog({ open: false })}>
                  Cancel
               </Button>
               <Button
                  onClick={() =>
                     deleteConfirmDialog.field &&
                     handleDeleteField(deleteConfirmDialog.field)
                  }
                  color="error"
                  variant="contained"
               >
                  Delete
               </Button>
            </DialogActions>
         </Dialog>
      </Box>
   );
}
