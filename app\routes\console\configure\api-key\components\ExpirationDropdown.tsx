import { useState } from 'react';
import {
  FormControl,
  Select,
  MenuItem,
  Typography,
  Box,
  TextField,
  Stack,
  SelectChangeEvent
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterMoment } from '@mui/x-date-pickers/AdapterMoment';
import moment, { Moment } from 'moment';
import { EXPIRATION_OPTIONS } from 'hooks/api/useAccesskey';

interface ExpirationDropdownProps {
  value: number;
  onChange: (days: number) => void;
  showLabel?: boolean;
}

export default function ExpirationDropdown({ value, onChange, showLabel = true }: ExpirationDropdownProps) {
  const [customDate, setCustomDate] = useState<Moment | null>(null);
  const [isCustom, setIsCustom] = useState(false);

  const handleChange = (event: SelectChangeEvent<number>) => {
    const selectedValue = event.target.value as number;
    
    if (selectedValue === -1) {
      // Custom option selected
      setIsCustom(true);
      const defaultCustomDays = 30;
      onChange(defaultCustomDays);
      setCustomDate(moment().add(defaultCustomDays, 'days'));
    } else {
      setIsCustom(false);
      onChange(selectedValue);
    }
  };

  const handleCustomDateChange = (newDate: Moment | null) => {
    setCustomDate(newDate);
    if (newDate) {
      // Calculate the difference from start of today to end of selected date
      // This ensures we include the full selected day
      const startOfToday = moment().startOf('day');
      const endOfSelectedDate = newDate.endOf('day');
      const daysFromNow = endOfSelectedDate.diff(startOfToday, 'days');
      onChange(Math.max(1, Math.ceil(daysFromNow)));
    }
  };

  const getDisplayValue = () => {
    if (isCustom) return -1;
    return value;
  };

  const getExpirationText = () => {
    if (value === 0) return '';
    if (isCustom && customDate) {
      return customDate.format('MMM D, YYYY');
    }
    const option = EXPIRATION_OPTIONS.find(opt => opt.days === value);
    if (option) {
      return `(${moment().add(value, 'days').format('MMM D, YYYY')})`;
    }
    return '';
  };

  return (
    <Stack spacing={1} sx={{ '& > *': { mb: 1.5 } }}>
      {showLabel && (
        <Typography variant="body1" fontWeight={600}>
          Expiration
        </Typography>
      )}
      
      <FormControl fullWidth>
        <Select
          value={getDisplayValue()}
          onChange={handleChange}
          displayEmpty
          sx={{
            backgroundColor: (theme) => theme.palette.background.paper,
            '& .MuiSelect-select': {
              py: 2
            },
            '& .MuiOutlinedInput-input': {
              py: 2
            }
          }}
        >
          {EXPIRATION_OPTIONS.map((option) => (
            <MenuItem key={option.days} value={option.days}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>
                <Typography variant="body2">
                  {option.label}
                </Typography>
                {option.days > 0 && (
                  <Typography variant="body2" color="text.secondary" sx={{ ml: 2 }}>
                    {moment().add(option.days, 'days').format('MMM D, YYYY')}
                  </Typography>
                )}
              </Box>
            </MenuItem>
          ))}
        </Select>
      </FormControl>

      {isCustom && (
        <Box sx={{ mt: 4 }}>
          <LocalizationProvider dateAdapter={AdapterMoment}>
            <DatePicker
              label="Select expiration date"
              value={customDate}
              onChange={handleCustomDateChange}
              minDate={moment().add(1, 'day')}
              slotProps={{
                textField: {
                  fullWidth: true,
                  sx: {
                    mt: 1,
                    '& .MuiInputBase-input': {
                      py: 2
                    },
                    '& .MuiIconButton-root': {
                      mr: 0
                    }
                  }
                }
              }}
            />
          </LocalizationProvider>
        </Box>
      )}

      {getExpirationText() && !isCustom && (
        <Typography variant="caption" color="text.secondary">
          Expires on {getExpirationText()}
        </Typography>
      )}
    </Stack>
  );
}