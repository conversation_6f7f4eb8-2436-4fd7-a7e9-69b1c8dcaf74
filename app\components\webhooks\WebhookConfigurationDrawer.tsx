import { useState, useMemo, useCallback, useEffect, useRef } from 'react';
import {
  Drawer,
  Box,
  Typography,
  IconButton,
  Stepper,
  Step,
  <PERSON>Label,
  Button,
  Stack,
  TextField,
  Checkbox,
  FormControlLabel,
  Alert,
  CircularProgress,
  Divider,
  Collapse,
  Chip,
  InputAdornment,
  Tooltip,
  FormHelperText,
  useTheme,
  alpha,
  StepConnector,
  stepConnectorClasses,
  styled
} from '@mui/material';
import {
  X,
  ChevronDown,
  ChevronRight,
  AlertTriangle,
  Check,
  Webhook as WebhookIcon,
} from 'lucide-react';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { toast } from 'sonner';
import { DOMAINS, type Domain } from 'data/domains';
import { Webhook, CreateWebhookPayload, UpdateWebhookPayload } from 'types/webhook';
import useUserDetails from 'store/user';
import { getEnvironmentId } from 'utils/environment-id';
import { webhookClient } from 'services/webhook.service';
import { useWebhooks } from 'hooks/api/webhooks/useWebhooks';

// Local event type for UI
type UIWebhookEvent = {
  key: string;
  label: string;
  description?: string;
  isEnabledByDefault?: boolean;
  warningWhenDisabled?: string;
};

// Domain type augmented with webhook metadata used by this UI
type WebhookDomain = Domain & {
  supportsWebhooks?: boolean;
  webhookEvents?: UIWebhookEvent[];
};

// Platform events that are always available
const PLATFORM_EVENTS: UIWebhookEvent[] = [
  {
    key: 'integration:created',
    label: 'Integration Created',
    description: 'When a new integration is successfully created',
    isEnabledByDefault: true,
    warningWhenDisabled: 'Connect UI requires this event to notify when integrations are created'
  },
  {
    key: 'integration:updated',
    label: 'Integration Updated',
    description: 'When an integration configuration is updated'
  },
  {
    key: 'integration:deleted',
    label: 'Integration Deleted',
    description: 'When an integration is removed'
  }
];

// Custom styled connector for stepper
const ColorlibConnector = styled(StepConnector)(({ theme }) => ({
  [`&.${stepConnectorClasses.alternativeLabel}`]: {
    top: 22,
  },
  [`&.${stepConnectorClasses.active}`]: {
    [`& .${stepConnectorClasses.line}`]: {
      backgroundColor: theme.palette.primary.main,
    },
  },
  [`&.${stepConnectorClasses.completed}`]: {
    [`& .${stepConnectorClasses.line}`]: {
      backgroundColor: theme.palette.primary.main,
    },
  },
  [`& .${stepConnectorClasses.line}`]: {
    height: 3,
    border: 0,
    backgroundColor: theme.palette.mode === 'light' ? theme.palette.grey[300] : theme.palette.grey[800],
    borderRadius: 1,
  },
}));

// Base form validation schema
const baseWebhookFormSchema = z.object({
  name: z.string()
    .min(1, 'Name is required')
    .transform((val) => val.trim()) // Trim whitespace before validation
    .pipe(z.string()
      .min(3, 'Name must be at least 3 characters')
      .max(50, 'Name must be less than 50 characters')
      .regex(/^[a-zA-Z0-9\s\-_\.]+$/, 'Name can only contain letters, numbers, spaces, hyphens, underscores, and dots')
      .regex(/^[a-zA-Z0-9]/, 'Name must start with a letter or number')
    ),
  events: z.array(z.string()).min(1, 'At least one event must be selected'),
  url: z.string().min(1, 'Endpoint URL is required'),
  description: z.string().max(500, 'Description must be less than 500 characters').optional()
});

type WebhookFormData = z.infer<typeof baseWebhookFormSchema>;

interface WebhookConfigurationDrawerProps {
  open: boolean;
  onClose: () => void;
  onSave: (data: CreateWebhookPayload | UpdateWebhookPayload) => Promise<void>;
  webhook?: Webhook;
  isEditMode?: boolean;
  userSubscriptions: string[];
}

export default function WebhookConfigurationDrawer({
  open,
  onClose,
  onSave,
  webhook,
  isEditMode = false,
  userSubscriptions = []
}: WebhookConfigurationDrawerProps) {
  const theme = useTheme();
  const { environments } = useUserDetails();
  const { attemptUpdateWebhook , attemptValidateWebhookEndpoint } = useWebhooks();
  const [activeStep, setActiveStep] = useState(0);
  const [expandedCategories, setExpandedCategories] = useState<Record<string, boolean>>({});
  const [selectedEvents, setSelectedEvents] = useState<string[]>([]);
  const [isValidatingUrl, setIsValidatingUrl] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [urlValidationError, setUrlValidationError] = useState<string | null>(null);
  const [showAllPlatformEvents, setShowAllPlatformEvents] = useState(false);
  const [urlValidationStatus, setUrlValidationStatus] = useState<'idle' | 'validating' | 'success' | 'error'>('idle');
  const [hasValidatedUrl, setHasValidatedUrl] = useState(false);
  const validatedUrlRef = useRef<string | null>(null);
  const debounceTimerRef = useRef<number | null>(null);
  const inFlightValidationRef = useRef(false);

  // Determine if current environment is production
  const environmentId = getEnvironmentId();
  const currentEnvironment = environments?.find((env: any) => env.id === environmentId);
  const isProduction = currentEnvironment?.type === 'LIVE' || false;
  
  // Create schema based on environment
  const webhookFormSchema = useMemo(() => {
    return z.object({
      name: z.string()
        .min(1, 'Name is required')
        .transform((val) => val.trim()) // Trim whitespace before validation
        .pipe(z.string()
          .min(3, 'Name must be at least 3 characters')
          .max(50, 'Name must be less than 50 characters')
          .regex(/^[a-zA-Z0-9\s\-_\.]+$/, 'Name can only contain letters, numbers, spaces, hyphens, underscores, and dots')
          .regex(/^[a-zA-Z0-9]/, 'Name must start with a letter or number')
        ),
      events: z.array(z.string()).min(1, 'At least one event must be selected'),
      url: z.string()
        .min(1, 'Endpoint URL is required')
        .refine((url) => {
          try {
            const urlObj = new URL(url);
            const validProtocols = isProduction ? ['https:'] : ['http:', 'https:'];
            return validProtocols.includes(urlObj.protocol);
          } catch {
            return false;
          }
        }, isProduction 
          ? 'Production environments require HTTPS URLs' 
          : 'Please enter a valid HTTP or HTTPS URL'),
      description: z.string().max(500, 'Description must be less than 500 characters').optional()
    });
  }, [isProduction]);

  const {
    control,
    handleSubmit,
    setValue,
    watch,
    reset,
    formState,
    trigger,
    clearErrors
  } = useForm<WebhookFormData>({
    resolver: zodResolver(webhookFormSchema),
    mode: 'onSubmit', // Only validate after field is touched
    reValidateMode: 'onSubmit', // Re-validate on change after initial validation
    defaultValues: {
      name: '',
      events: [],
      url: '',
      description: ''
    }
  });

  const watchedUrl = watch('url');
  const watchedDescription = watch('description');

  // Auto-validate URL shortly after user stops typing
  useEffect(() => {
    // Skip if empty or already validated to the same value
    if (activeStep !== 1) return;
    if (!watchedUrl || watchedUrl === validatedUrlRef.current) return;

    // Only proceed if it looks like a valid URL structure
    try {
      new URL(watchedUrl);
    } catch {
      return;
    }

    // Clear any existing debounce
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
      debounceTimerRef.current = null;
    }

    // Debounce validation
    debounceTimerRef.current = window.setTimeout(() => {
      // Validate latest value
      validateEndpoint(watchedUrl);
      debounceTimerRef.current = null;
    }, 600);

    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
        debounceTimerRef.current = null;
      }
    };
  }, [watchedUrl, activeStep]);

  // Get available domains based on user subscriptions
  const availableDomains = useMemo(() => {
    const domains: WebhookDomain[] = [];
    
    // Always include Platform
    const platformDomain = (DOMAINS as WebhookDomain[]).find(d => d.key === 'PLATFORM');
    if (platformDomain) {
      domains.push({
        ...platformDomain,
        webhookEvents: PLATFORM_EVENTS
      });
    }

    // Add subscribed domains
    userSubscriptions.forEach(subCode => {
      const domain = (DOMAINS as WebhookDomain[]).find(d => 
        d.supportsWebhooks && 
        (d.subscription?.code === subCode || d.key === subCode.toUpperCase())
      );
      if (domain) {
        domains.push(domain);
      }
    });

    return domains;
  }, [userSubscriptions]);

  // Initialize form with existing webhook data and expand all categories by default
  useEffect(() => {
    if (!open) {
      // Reset everything when drawer closes
      setActiveStep(0);
      setSelectedEvents([]);
      setIsValidatingUrl(false);
      setIsSaving(false);
      setUrlValidationError(null);
      setShowAllPlatformEvents(false);
      setExpandedCategories({});
      setUrlValidationStatus('idle');
      setHasValidatedUrl(false);
      validatedUrlRef.current = null;
      reset({
        name: '',
        events: [],
        url: '',
        description: ''
      });
      return;
    }

    // Initialize with all categories expanded
    const initialExpanded: Record<string, boolean> = {};
    availableDomains.forEach(domain => {
      initialExpanded[domain.key] = true;
    });
    

     if (webhook && isEditMode) {
      const events = (webhook as any).eventSubscriptions?.eventTypes || [];
      const webhookUrl = (webhook as any).destination?.webhookConfig?.url || '';
      
      setSelectedEvents(events);
      reset({
        name: webhook.name || '',
        events,
        url: webhookUrl,
        description: (webhook as any).description || ''
      });
      setExpandedCategories(initialExpanded);
      
      // In edit mode, consider the existing URL as already validated
      // This prevents unnecessary validation on load
      if (webhookUrl) {
        validatedUrlRef.current = webhookUrl;
        setUrlValidationStatus('success');
      }
    } else {
      // For new webhooks, expand all categories and select default events
      const defaultEvents: string[] = [];
      
      // Add default platform events
      PLATFORM_EVENTS.forEach(event => {
        if (event.isEnabledByDefault) {
          defaultEvents.push(event.key);
        }
      });
      
      // Add default events from other domains
      availableDomains.forEach(domain => {
        domain.webhookEvents?.forEach(event => {
          if (event.isEnabledByDefault) {
            defaultEvents.push(event.key);
          }
        });
      });
      
      setSelectedEvents(defaultEvents);
      setValue('events', defaultEvents);
      setExpandedCategories(initialExpanded);
    }
  }, [open, webhook, isEditMode, reset, availableDomains, setValue]);

  const handleCategoryToggle = (categoryKey: string) => {
    setExpandedCategories(prev => ({
      ...prev,
      [categoryKey]: !prev[categoryKey]
    }));
  };

  const handleEventToggle = (eventKey: string) => {
    const newEvents = selectedEvents.includes(eventKey)
      ? selectedEvents.filter(e => e !== eventKey)
      : [...selectedEvents, eventKey];
    
    setSelectedEvents(newEvents);
    setValue('events', newEvents);
  };

  const handleSelectAllCategory = (domain: WebhookDomain) => {
    const categoryEvents = (domain.webhookEvents || []).map((e: UIWebhookEvent) => e.key);
    const allSelected = categoryEvents.every(e => selectedEvents.includes(e));
    
    let newEvents: string[];
    if (allSelected) {
      // Deselect all events from this category
      newEvents = selectedEvents.filter(e => !categoryEvents.includes(e));
    } else {
      // Select all events from this category
      newEvents = [...new Set([...selectedEvents, ...categoryEvents])];
    }
    
    setSelectedEvents(newEvents);
    setValue('events', newEvents);
  };

  const validateEndpoint = async (url: string, isRetry: boolean = false) => {
    // Don't validate if already validated successfully (unless retry)
    if (!isRetry && validatedUrlRef.current === url && urlValidationStatus === 'success') {
      return;
    }
    // Prevent overlapping validations
    if (isValidatingUrl || inFlightValidationRef.current || urlValidationStatus === 'validating') {
      return;
    }
    
    setIsValidatingUrl(true);
    inFlightValidationRef.current = true;
    setUrlValidationError(null);
    setUrlValidationStatus('validating');
    setHasValidatedUrl(true);
    
    try {
      // First validate URL format
      const urlObj = new URL(url);
      
      // Check protocol based on environment
      if (isProduction && urlObj.protocol === 'http:') {
        throw new Error('Production environments require HTTPS URLs for security');
      }
      
      // Check for localhost in production
      if (isProduction && (urlObj.hostname === 'localhost' || urlObj.hostname === '127.0.0.1')) {
        throw new Error('Localhost URLs are not accessible in production. Please use a publicly accessible URL.');
      }
      
      // Make actual validation request via centralized helper
      const result = await attemptValidateWebhookEndpoint(url);
      if (result?.ok !== true) {
        const serverMessage = result?.message || (result as any)?.error || result?.statusText;
        const statusPart = typeof result?.status === 'number' ? ` (${result.status})` : '';
        throw new Error((serverMessage || 'Endpoint validation failed') + statusPart);
      }
      setUrlValidationStatus('success');
      validatedUrlRef.current = url;
      toast.success('Endpoint validated successfully');
      
    } catch (error: any) {
      setUrlValidationStatus('error');
      
      // Handle network errors
      if (error.name === 'TypeError' && error.message === 'Failed to fetch') {
        setUrlValidationError('Network error. Please check your internet connection and try again.');
      } else if (error.message) {
        setUrlValidationError(error.message);
      } else {
        setUrlValidationError('Failed to validate endpoint. Please ensure the URL is correct and accessible.');
      }
    } finally {
      setIsValidatingUrl(false);
      inFlightValidationRef.current = false;
    }
  };

  const handleNext = () => {
    event?.preventDefault();
    event?.stopPropagation();
    if (activeStep === 0) {
      // Validate that at least one event is selected
      if (selectedEvents.length === 0) {
        toast.error('Please select at least one event');
        return;
      }
    }
    setActiveStep(prev => prev + 1);
  };

  const handleBack = () => {
    setActiveStep(prev => prev - 1);
  };

  const handleCopy = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success('Copied to clipboard');
  };

  const onSubmitForm = async (data: WebhookFormData) => {
    // Ensure URL is validated before saving
      if (activeStep !== steps.length - 1) {
      event?.preventDefault();
     event?.stopPropagation();
      return;
    }

    // Validate all fields before submission
    const isValid = await trigger();
    if (!isValid) {
      return;
    }
    if (urlValidationStatus !== 'success' || validatedUrlRef.current !== data.url) {
      toast.error('Please validate your endpoint URL before proceeding');
      await validateEndpoint(data.url);
      return;
    }
    
    setIsSaving(true);
    try {
      // Build payload according to provided schema
          console.log(Array.from(new Set(data.events)),"Array.from(new Set(data.events))");

      const payload = {
        name: data.name.trim(), // Name is already trimmed by zod, but being explicit
        type: 'WEBHOOK_ENDPOINT',
        description: data.description?.trim() || undefined, // Trim description and convert empty string to undefined
        destination: {
          type: 'WEBHOOK',
          webhookConfig: {
            url: data.url.trim(),
            contentType: 'application/json',
            sslVerification: true
          }
        },
        eventSubscriptions: {
          // Ensure unique, deterministic ordering
          eventTypes: Array.from(new Set(data.events))
        }
      } as const;

      if (isEditMode && webhook?.id) {
        // Build JSON Patch ops by comparing current webhook with form data
        const patchOps: Array<{ op: 'replace'; path: string; value: any }> = [];
         //Name
         const currentName = (webhook as any).name || '';
         if (payload.name !== currentName) {
          patchOps.push({ op: 'replace', path: '/name', value: payload.name });
        }
        // Description
const currentDescription = (webhook as any).description ?? undefined; 
const newDescription = (payload as any).description ?? undefined;     

if (newDescription !== currentDescription) {
  if (newDescription === undefined) {
    // If description is cleared, remove it
    patchOps.push({ op: 'replace', path: '/description', value: '' });
  } else {
    // Otherwise, update with the new description
    patchOps.push({ op: 'replace', path: '/description', value: newDescription });
  }
}

        // Endpoint URL
        const currentUrl = (webhook as any).destination?.webhookConfig?.url || '';
        const newUrl = (payload as any).destination?.webhookConfig?.url || '';
        if (newUrl !== currentUrl) {
          patchOps.push({ op: 'replace', path: '/destination/webhookConfig/url', value: newUrl });
        }

        // Event subscriptions (replace entire list to avoid index juggling)
        const currentEvents = (webhook as any).eventSubscriptions?.eventTypes || [];
        const nextEvents = (payload as any).eventSubscriptions?.eventTypes || [];
        if (JSON.stringify(currentEvents) !== JSON.stringify(nextEvents)) {
          patchOps.push({ op: 'replace', path: '/eventSubscriptions/eventTypes', value: nextEvents });
        }
        
        if (patchOps.length === 0) {
          toast.info('No changes detected');
          setIsSaving(false);
          return;
        }

        await attemptUpdateWebhook(patchOps as unknown as Record<string, any>, webhook.id, () => {
          onClose();
        });
      } else {
        // Create
        await onSave(payload as unknown as CreateWebhookPayload);
        onClose();
      }
    } catch (error: any) {
      toast.error(error?.response?.data?.message || error?.message || 'Failed to save webhook');
    } finally {
      setIsSaving(false);
    }
  };

  const steps = ['Select Events', 'Configure Endpoints'];

  const renderEventSelection = () => {
    const platformDomain = (availableDomains as WebhookDomain[]).find(d => d.key === 'PLATFORM');
    const otherDomains = (availableDomains as WebhookDomain[]).filter(d => d.key !== 'PLATFORM');

    return (
      <Box>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
          Select the events you want to receive notifications for. Events are grouped by category based on your active subscriptions.
        </Typography>

        <Stack spacing={2}>
          {/* Platform Events - Always shown first */}
          {platformDomain && (
            <Box
              sx={{
                border: `1px solid ${theme.palette.divider}`,
                borderRadius: 1,
                overflow: 'hidden'
              }}
            >
              <Box
                sx={{
                  p: 2,
                  backgroundColor: alpha(theme.palette.primary.main, 0.04),
                  cursor: 'pointer',
                  '&:hover': {
                    backgroundColor: alpha(theme.palette.primary.main, 0.08)
                  }
                }}
                onClick={() => handleCategoryToggle('PLATFORM')}
              >
                <Stack direction="row" alignItems="center" justifyContent="space-between">
                  <Stack direction="row" alignItems="center" spacing={2}>
                    <Box
                      sx={{
                        width: 40,
                        height: 40,
                        borderRadius: 1,
                        backgroundColor: alpha(theme.palette.primary.main, 0.1),
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center'
                      }}
                    >
                      <WebhookIcon size={20} color={theme.palette.primary.main} />
                    </Box>
                    <Box>
                      <Typography variant="subtitle1" fontWeight={600}>
                        Platform
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {PLATFORM_EVENTS.length} events available
                      </Typography>
                    </Box>
                  </Stack>
                  <Stack direction="row" alignItems="center" spacing={1}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={PLATFORM_EVENTS.every(e => selectedEvents.includes(e.key))}
                          indeterminate={
                            PLATFORM_EVENTS.some(e => selectedEvents.includes(e.key)) &&
                            !PLATFORM_EVENTS.every(e => selectedEvents.includes(e.key))
                          }
                          onChange={() => handleSelectAllCategory(platformDomain)}
                          onClick={(e) => e.stopPropagation()}
                          size="small"
                        />
                      }
                      label="Select all"
                      sx={{ mr: 1 }}
                      onClick={(e) => e.stopPropagation()}
                    />
                    <IconButton size="small">
                      {expandedCategories['PLATFORM'] ? <ChevronDown size={20} /> : <ChevronRight size={20} />}
                    </IconButton>
                  </Stack>
                </Stack>
              </Box>

              <Collapse in={expandedCategories['PLATFORM']}>
                <Box sx={{ p: 2, backgroundColor: theme.palette.background.paper }}>
                  <Stack spacing={1}>
                    {PLATFORM_EVENTS.slice(0, showAllPlatformEvents ? undefined : 3).map(event => (
                      <Box key={event.key}>
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={selectedEvents.includes(event.key)}
                              onChange={() => handleEventToggle(event.key)}
                              size="small"
                            />
                          }
                          label={
                            <Box>
                              <Stack direction="row" alignItems="center" spacing={1}>
                                <Typography variant="body2" fontWeight={500}>
                                  {event.label}
                                </Typography>
                                <Chip 
                                  label={event.key}
                                  size="small"
                                  sx={{ 
                                    height: 20,
                                    fontSize: '0.7rem',
                                    backgroundColor: alpha(theme.palette.text.primary, 0.08)
                                  }}
                                />
                              </Stack>
                              <Typography variant="caption" color="text.secondary">
                                {event.description}
                              </Typography>
                              {((event as any).warningMessage && !selectedEvents.includes(event.key)) || 
                                (event.warningWhenDisabled && !selectedEvents.includes(event.key)) ? (
                                <Alert
                                  severity="warning"
                                  icon={<AlertTriangle size={14} />}
                                  sx={{ 
                                    mt: 0.5,
                                    py: 0,
                                    '& .MuiAlert-icon': {
                                      py: 0.5
                                    }
                                  }}
                                >
                                  <Typography variant="caption">
                                    {(event as any).warningMessage || event.warningWhenDisabled}
                                  </Typography>
                                </Alert>
                              ) : null}
                            </Box>
                          }
                          sx={{ alignItems: 'flex-start', mb: 1.5 }}
                        />
                      </Box>
                    ))}
                    {PLATFORM_EVENTS.length > 3 && !showAllPlatformEvents && (
                      <Button
                        size="small"
                        onClick={() => setShowAllPlatformEvents(true)}
                        sx={{ textTransform: 'none' }}
                      >
                        Show {PLATFORM_EVENTS.length - 3} more events
                      </Button>
                    )}
                  </Stack>
                </Box>
              </Collapse>
            </Box>
          )}

          {/* Other Domain Events */}
          {otherDomains.map(domain => {
            const events = (domain as WebhookDomain).webhookEvents || [];
            const Icon = domain.icon;
            const categorySelectedCount = events.filter(e => selectedEvents.includes(e.key)).length;

            return (
              <Box
                key={domain.key}
                sx={{
                  border: `1px solid ${theme.palette.divider}`,
                  borderRadius: 1,
                  overflow: 'hidden'
                }}
              >
                <Box
                  sx={{
                    p: 2,
                    backgroundColor: expandedCategories[domain.key] 
                      ? alpha(theme.palette.background.default, 0.5)
                      : theme.palette.background.paper,
                    cursor: 'pointer',
                    '&:hover': {
                      backgroundColor: alpha(theme.palette.action.hover, 0.04)
                    }
                  }}
                  onClick={() => handleCategoryToggle(domain.key)}
                >
                  <Stack direction="row" alignItems="center" justifyContent="space-between">
                    <Stack direction="row" alignItems="center" spacing={2}>
                      <Box
                        sx={{
                          width: 40,
                          height: 40,
                          borderRadius: 1,
                          backgroundColor: alpha(theme.palette.text.primary, 0.04),
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center'
                        }}
                      >
                        <Icon size={20} color={theme.palette.text.secondary} />
                      </Box>
                      <Box>
                        <Stack direction="row" alignItems="center" spacing={1}>
                          <Typography variant="subtitle1" fontWeight={600}>
                            {domain.label}
                          </Typography>
                          {categorySelectedCount > 0 && (
                            <Chip
                              label={`${categorySelectedCount} selected`}
                              size="small"
                              color="primary"
                              sx={{ height: 20 }}
                            />
                          )}
                        </Stack>
                        <Typography variant="caption" color="text.secondary">
                          {events.length} events available
                        </Typography>
                      </Box>
                    </Stack>
                    <Stack direction="row" alignItems="center" spacing={1}>
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={events.every(e => selectedEvents.includes(e.key))}
                            indeterminate={
                              events.some(e => selectedEvents.includes(e.key)) &&
                              !events.every(e => selectedEvents.includes(e.key))
                            }
                            onChange={() => handleSelectAllCategory(domain)}
                            onClick={(e) => e.stopPropagation()}
                            size="small"
                          />
                        }
                        label="Select all"
                        sx={{ mr: 1 }}
                        onClick={(e) => e.stopPropagation()}
                      />
                      <IconButton size="small">
                        {expandedCategories[domain.key] ? <ChevronDown size={20} /> : <ChevronRight size={20} />}
                      </IconButton>
                    </Stack>
                  </Stack>
                </Box>

                <Collapse in={expandedCategories[domain.key]}>
                  <Box sx={{ p: 2, backgroundColor: theme.palette.background.paper }}>
                    <Stack spacing={1}>
                      {events.map(event => (
                        <FormControlLabel
                          key={event.key}
                          control={
                            <Checkbox
                              checked={selectedEvents.includes(event.key)}
                              onChange={() => handleEventToggle(event.key)}
                              size="small"
                            />
                          }
                          label={
                            <Box>
                              <Stack direction="row" alignItems="center" spacing={1}>
                                <Typography variant="body2" fontWeight={500}>
                                  {event.label}
                                </Typography>
                                <Chip 
                                  label={event.key}
                                  size="small"
                                  sx={{ 
                                    height: 20,
                                    fontSize: '0.7rem',
                                    backgroundColor: alpha(theme.palette.text.primary, 0.08)
                                  }}
                                />
                              </Stack>
                              <Typography variant="caption" color="text.secondary">
                                {event.description}
                              </Typography>
                            </Box>
                          }
                          sx={{ alignItems: 'flex-start', mb: 1.5 }}
                        />
                      ))}
                    </Stack>
                  </Box>
                </Collapse>
              </Box>
            );
          })}
        </Stack>
      </Box>
    );
  };


  const renderEndpointConfiguration = () => (
    <Box>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Configure your webhook endpoint to receive events.
      </Typography>

      <Stack spacing={3}>
        {/* Name */}
        <Box>
          <Typography variant="body1" fontWeight={600} sx={{ mb: 0.5 }}>
            Name
          </Typography>
          <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 1 }}>
            Give your webhook a unique name to easily identify it later
          </Typography>
          <Controller
            name="name"
            control={control}
            render={({ field, fieldState }) => (
              <TextField
                {...field}
                fullWidth
                placeholder="e.g., Production Event Handler"
                error={!!fieldState.error}  // show error whenever it exists
                 helperText={fieldState.error?.message || 'Letters, numbers, spaces, hyphens, underscores, and dots are allowed'}
                inputProps={{
                  maxLength: 50
                }}
              />
            )}
          />
        </Box>

        {/* Endpoint URL */}
        <Box>
          <Typography variant="body1" fontWeight={600} sx={{ mb: 0.5 }}>
            Endpoint URL
          </Typography>
          <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 1 }}>
            Webhooks require a live endpoint URL to send events to. {!isProduction && 'HTTP and HTTPS URLs are supported.'}
          </Typography>
          <Controller
            name="url"
            control={control}
            render={({ field, fieldState }) => (
              <TextField
                {...field}
                fullWidth
                placeholder="https://my-webhook.endpoint.net"
                error={(!!fieldState.error && fieldState.isDirty) || !!urlValidationError}
                helperText={(fieldState.isDirty && fieldState.error?.message) || urlValidationError}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      {isValidatingUrl && <CircularProgress size={20} />}
                      {!isValidatingUrl && urlValidationStatus === 'success' && (
                        <Tooltip title="Endpoint validated">
                          <Box sx={{ display: 'flex', alignItems: 'center', color: theme.palette.success.main }}>
                            <Check size={20} />
                          </Box>
                        </Tooltip>
                      )}
                      {!isValidatingUrl && urlValidationStatus === 'error' && watchedUrl && (
                        <Tooltip title="Retry validation">
                          <IconButton
                            size="small"
                            onClick={() => validateEndpoint(watchedUrl, true)}
                            sx={{ color: theme.palette.error.main }}
                          >
                            <AlertTriangle size={18} />
                          </IconButton>
                        </Tooltip>
                      )}
                    </InputAdornment>
                  )
                }}
                onBlur={() => {
                  // Cancel any pending debounce and validate once on blur
                  if (debounceTimerRef.current) {
                    clearTimeout(debounceTimerRef.current);
                    debounceTimerRef.current = null;
                  }
                  if (field.value && field.value !== validatedUrlRef.current) {
                    validateEndpoint(field.value);
                  }
                }}
                onChange={(e) => {
                  field.onChange(e);
                  // Reset validation status when URL changes
                  if (e.target.value !== validatedUrlRef.current) {
                    setUrlValidationStatus('idle');
                    setUrlValidationError(null);
                  }
                }}
              />
            )}
          />
          {isProduction && (
            <Typography variant="caption" color="warning.main" sx={{ mt: 0.5, display: 'block' }}>
              Only HTTPS URLs are allowed.
            </Typography>
          )}
        </Box>

        {/* Description */}
        <Box>
          <Typography variant="body1" fontWeight={600} sx={{ mb: 0.5 }}>
            Description
            <Typography component="span" variant="caption" color="text.secondary" sx={{ ml: 1, fontWeight: 400 }}>
              Optional
            </Typography>
          </Typography>
          <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 1 }}>
            <Typography variant="caption" color="text.secondary">
              Add additional context about this webhook's purpose or behavior
            </Typography>
            <Typography 
              variant="caption" 
              color={(watchedDescription?.length ?? 0) > 500 ? 'error.main' : 'text.secondary'}
            >
              {(watchedDescription?.length ?? 0)}/500
            </Typography>
          </Stack>
          <Controller
            name="description"
            control={control}
            render={({ field, fieldState }) => (
              <TextField
                {...field}
                fullWidth
                multiline
                rows={3}
                placeholder="e.g., Handles production environment events for new repository creation"
                error={!!fieldState.error && fieldState.isDirty}
                helperText={fieldState.isDirty && fieldState.error?.message}
                inputProps={{
                  maxLength: 500
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    fontSize: '0.875rem'
                  }
                }}
              />
            )}
          />
        </Box>

      </Stack>
    </Box>
  );

  return (
    <Drawer
      anchor="right"
      open={open}
      PaperProps={{
        sx: {
          width: { xs: '100%', sm: '90%', md: '800px' },
          maxWidth: '800px'
        }
      }}
    >
      <form onSubmit={handleSubmit(onSubmitForm)} style={{ height: '100%' }}>
        <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
          {/* Header */}
          <Box sx={{ p: 3, borderBottom: `1px solid ${theme.palette.divider}` }}>
            <Stack direction="row" alignItems="center" justifyContent="space-between">
              <Box sx={{ flex: 1 }}>
                <Typography variant="h5" fontWeight={600}>
                  {isEditMode ? 'Edit Webhook' : 'Add Webhook'}
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                  {isEditMode 
                    ? 'Update your webhook configuration and event subscriptions'
                    : 'Configure real-time notifications for events in your integrations'
                  }
                </Typography>
              </Box>
              <IconButton onClick={onClose} size="small" sx={{ ml: 2 }}>
                <X size={20} />
              </IconButton>
            </Stack>
          </Box>

          {/* Stepper */}
          <Box sx={{ px: 3, pt: 3 }}>
            <Stepper activeStep={activeStep} connector={<ColorlibConnector />}>
              {steps.map((label, index) => (
                <Step key={label}>
                  <StepLabel>{label}</StepLabel>
                </Step>
              ))}
            </Stepper>
          </Box>

          {/* Content */}
          <Box sx={{ 
            flex: 1, 
            overflow: 'auto', 
            p: 3,
            pb: '100px' // Add padding bottom to prevent content from being hidden behind sticky footer
          }}>
            {activeStep === 0 && renderEventSelection()}
            {activeStep === 1 && renderEndpointConfiguration()}
          </Box>

          {/* Footer - Sticky Navigation */}
          <Box sx={{ 
            position: 'sticky',
            bottom: 0,
            left: 0,
            right: 0,
            p: 3, 
            borderTop: `1px solid ${theme.palette.divider}`,
            backgroundColor: theme.palette.background.paper,
            boxShadow: '0 -4px 12px rgba(0, 0, 0, 0.04)',
            zIndex: 1
          }}>
            <Stack direction="row" spacing={2} justifyContent="space-between">
              <Button
                variant="outlined"
                onClick={activeStep === 0 ? onClose : handleBack}
                disabled={isSaving}
              >
                {activeStep === 0 ? 'Cancel' : 'Back'}
              </Button>
              
              <Box>
                {activeStep < steps.length - 1 ? (
                  <Button
                    type="button"
                    variant="contained"
                    onClick={handleNext}
                    disabled={activeStep === 0 && selectedEvents.length === 0}
                    endIcon={<ChevronRight size={18} />}
                  >
                    Next
                  </Button>
                ) : (
                  <Button
                    type="submit"
                    variant="contained"
                    disabled={isSaving || selectedEvents.length === 0 || (Boolean(watchedUrl) && urlValidationStatus !== 'success')}
                    startIcon={isSaving ? <CircularProgress size={18} /> : undefined}
                  >
                    {(isEditMode ? 'Update Webhook' : 'Create Webhook')}
                  </Button>
                )}
              </Box>
            </Stack>
          </Box>
        </Box>
      </form>
    </Drawer>
  );
}