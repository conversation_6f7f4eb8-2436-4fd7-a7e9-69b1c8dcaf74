import { Grid, GridProps, Radio, Stack, Typography } from "@mui/material";
import MainCard from "components/MainCard";
import { AccessPointConfigType } from "hooks/api/use-accesspoint-config/useGetAccessPoint";
import MenuOption from "../menu-option";
import { GRID } from "./constants";
import Placeholder from "../skeleton";
import { useEffect, useState } from "react";
import useManageProfile from "sections/services/service-context/use-manage-profile";
import BoxSelect from "components/box-select";
import ConnectorConfigurationSummary from "./ConnectorConfigurationSummary";

type Props = {
   gridItemProps?: GridProps
   mode?: 'edit' | 'view'
   selected?: string[]
   onSelect?: (selected: string[], unSelected: string | null) => void
   onEdit?: any
}

export default ({
   gridItemProps = {},
   mode = 'view',
   selected: selectedProp,
   onSelect: onSelectProp,
   onEdit
}: Props) => {

   const {
      serviceProfileAccessPoints: data,
      isLoadingAccessPoints: isLoading,
      selectedService
   } = useManageProfile()

   const [localSelected, selectedLocalSelected] = useState<string[]>([]);

   const selected = selectedProp ?? localSelected;

   useEffect(() => {
      if (data?.length === 1 && !selected.includes(data[0]?.accessPoint?.id)) {
         // Automatically select the only available item
         const singleItemId = data[0]?.accessPoint?.id;
         selectedLocalSelected([singleItemId]);
         if (typeof onSelectProp === 'function') {
           onSelectProp([singleItemId], null);
         }
       }
    }, [data, onSelectProp, selected]);

    const onSelect = (_: any, { selected: id }: any) => {
      if (data?.length === 1) {
        // Prevent deselection if only one option exists
        return;
      }

      let updated: string[] = selected;
      let unSelected: string | null = null;

      if (selected.includes(id)) {
        updated = updated.filter((i) => id !== i);
        unSelected = id;
      } else {
        updated = updated.concat(id);
      }

      selectedLocalSelected(updated);
      if (typeof onSelectProp === 'function') {
        onSelectProp(updated, unSelected);
      }
    };

    if (isLoading) {
      return <Placeholder />;
    }

    if (data?.length <= 0 && !isLoading) {
      return null;
    }

   // Use modern summary view in view mode
   if (mode === 'view') {
      return (
         <ConnectorConfigurationSummary
            serviceProfile={selectedService}
            accessPoints={data}
            onEdit={onEdit}
            onConfigureAuth={(authMethod) => {
               // Handle auth configuration navigation
               // You can add navigation logic here
            }}
         />
      );
   }

   // Edit mode - keep the existing BoxSelect functionality
   return (
      <BoxSelect
         isMulti
         selectedItems={selected}
         gridItemProps={{ ...GRID, ...gridItemProps }}
         items={(
            data.map((item: any) => {
               return {
                  title: item?.label,
                  description: item?.description,
                  value: item?.accessPoint?.id ?? 'not_yet',
                  ...item
               }
            })
         )}
         renderActions={(item: any) => {
            return (
               ((AccessPointConfigType.AppFlow === item?.type) || (AccessPointConfigType.OAuthFlow === item?.type)) && (
                  <MenuOption item={item} />
               )
            )
         }}
         onSelectItems={onSelect}
      />
   )
}


type ItemProps = {
   item: Record<string, any>
   canEdit?: boolean
   selected: boolean
} & GridProps;

const Item = (props: ItemProps) => {

   const { item, canEdit, selected, sx = {}, ...rest } = props;

   return (
      <Grid
         item
         {...GRID}
         {...rest}
         xs={12}
         sx={{
            userSelect: 'none',
            ...sx,
         }}
      >
         <MainCard  >
            <Stack direction={'row'} alignItems={'flex-start'} gap={1}>
               {/* left */}
               <Radio checked={selected} disabled={!canEdit} />

               {/* right */}
               <Stack gap={.5} >
                  <Stack direction={'row'} justifyContent={'space-between'}>
                     <Typography variant="h5" className="font-semibold">
                        {item?.label}
                     </Typography>
                     {(AccessPointConfigType.AppFlow === item?.type && !canEdit) && (
                        <MenuOption item={item} />
                     )}
                  </Stack>
                  <Typography variant="h6" color={'secondary.600'}>
                     {item?.description}
                  </Typography>
               </Stack>
            </Stack>
         </MainCard>
      </Grid>
   )
}