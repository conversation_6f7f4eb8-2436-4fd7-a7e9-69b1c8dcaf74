import { keepPreviousData, useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { ColumnFiltersState } from "@tanstack/react-table";

import { IntegrationClient } from "../../../services/integration.service";

import { ExtendedTablePagination, Pagination } from "types/utils";
import { API_ENDPOINTS } from "utils/api/api-endpoints";
import moment from "moment";
import { Tooltip, Typography } from "@mui/material";
import { parseError } from "lib/utils";
import { toast } from "sonner";
import { parseLogFilterPayload } from "../log/helper";
import { parseIntegrationFilterPayload } from "./helper";


export type SPQueryOptionsType = {
   offset?: number;
   limit?: number;
   orderBy?: string;
};

export enum MonitorEnum {
   // EVERY_2_MINS = 'EVERY_2_MINS',
   // EVERY_5_MINS = 'EVERY_5_MINS',
   EVERY_6_HOURS = 'EVERY_6_HOURS',
   DAILY = 'DAILY',
   WEEKLY = 'WEEKLY',
   MONTHLY = 'MONTHLY',
   CUSTOM = 'CUSTOM',
};

const options = [
   // { value: MonitorEnum.EVERY_2_MINS, label: 'Every 2 mins' },
   // { value: MonitorEnum.EVERY_5_MINS, label: 'Every 5 mins' },
   { value: MonitorEnum.EVERY_6_HOURS, label: 'Every 6 hours' },
   { value: MonitorEnum.DAILY, label: 'Daily' },
   { value: MonitorEnum.WEEKLY, label: 'Weekly' },
   { value: MonitorEnum.MONTHLY, label: 'Monthly' }
];

const CREATE_CONFIG_TYPE = 'SCHEDULER';

export const useGetIntegration = (props: Record<string, any>) => {

   const orgId = props?.orgId ?? ''

   const queryClient = useQueryClient();

   const { data: configResp } = useQuery({
      queryKey: [API_ENDPOINTS.INTEGRATION_MONITOR, orgId],
      queryFn: async () => {
         return await IntegrationClient.getIntegrationMonitorConfig(orgId as string)
      },
      enabled: !!orgId
   });
   const monitorConfig = configResp?.data;

   const {
      mutateAsync: createMonitorConfigMutation,
   } = useMutation({
      mutationFn: (value: MonitorEnum) => {
         return IntegrationClient.createIntegrationMonitorConfig({
            "type": CREATE_CONFIG_TYPE,
            "schedulerTypeConfig": {
               "type": value
            }
         })
      },
      mutationKey: [API_ENDPOINTS.INTEGRATION_MONITOR],
   });

   const {
      mutateAsync: updateMonitorConfigMutation,
   } = useMutation({
      mutationFn: (value: MonitorEnum) => {
         return IntegrationClient.updateIntegrationMonitorConfig(monitorConfig?.id,
            [{ op: "replace", path: "/schedulerTypeConfig/type", value }]
         )
      },
      mutationKey: [API_ENDPOINTS.INTEGRATION_MONITOR],
   });


   const attemptToUpdateMonitorConfig = (type: MonitorEnum) => {
      const selectedOptionLabel = options.find((option) => option.value === type)?.label || 'Unknown';
      toast.promise(updateMonitorConfigMutation(type), {
         loading: 'Updating...',
         success: () => {
            queryClient.invalidateQueries({ queryKey: [API_ENDPOINTS.INTEGRATION_MONITOR, orgId] })
            return <b>Activated for {selectedOptionLabel}</b>;;
         },
         error: (error: any) => {
            return <b>{"Not created successfully"}</b>;
         },
      })
   }

   const attemptToCreateMonitorConfig = (type: MonitorEnum) => {
      toast.promise(createMonitorConfigMutation(type), {
         loading: 'Creating...',
         success: () => {
            queryClient.invalidateQueries({ queryKey: [API_ENDPOINTS.INTEGRATION_MONITOR, orgId] })
            return <b>Created successfully</b>;
         },
         error: (error: any) => {
            return <b>{parseError(error?.response?.data)?.message}</b>;
         },
      })
   }

   return {
      deriveRateLimit: (integration: any) => {


         const rateLimit = integration?.rateLimit;

         const limit = rateLimit?.limit;
         const used = rateLimit?.used;

         const nextReset = moment(rateLimit?.resetDateTime).local().format('MMM D, YYYY hh:mm:ss A');
         const resetWindowTimeCrossed = rateLimit?.resetDateTime;

         let crossedFlag = false;
         let currentDate = moment();

         if (currentDate.isAfter(resetWindowTimeCrossed)) {
            crossedFlag = true;
         }

         const percentage = Number(((used / limit) * 100).toFixed(2));

         const getBackgroundColor = () => {
            if (percentage <= 50) {
               return "success" // Light green background
            } else if (percentage > 50 && percentage < 100) {
               return "warning" // Light orange background
            } else if (percentage >= 100) {
               return "danger" // Light red background
            }
         };

         const tooltipContent = rateLimit ? (
            <>
               Used:&nbsp; {crossedFlag ? 0 : used}&nbsp;/ &nbsp;{crossedFlag ? 1000 : limit} <br />
               Next Reset:&nbsp; {crossedFlag ? "To be determined" : nextReset}
            </>
         ) : "Start making API requests to get Ratelimit details"

         return {
            className: `rate_limit_${getBackgroundColor()}`,
            element: (
               <Tooltip title={tooltipContent} placement="top">
                  <Typography>
                     {rateLimit && !crossedFlag ? percentage : 0}%
                  </Typography>
               </Tooltip>
            )
         }
      },
      search: ({ 
         filter = [], 
         ...pagination 
      }: Partial<ExtendedTablePagination> & { filter?: ColumnFiltersState }) => {

         delete pagination?.total;

         const criteria = [
            {
               property: "/organization/id",
               operator: "=",
               values: [orgId],
            },
         ];

         // adding filter - use integration-specific helper for better date handling
         criteria.push(...parseIntegrationFilterPayload(filter));

         const payload = {
            filter: {
               and: criteria
            },
            pagination: {
               offset: pagination?.pageIndex || 0,
               limit: pagination?.pageSize || 10,
            },
            sort: [
               {
                  property: "/changeLog/lastUpdatedDateTime",
                  direction: "DESC"
               }
            ]
         }

         return useQuery(
            {
               queryKey: [
                  API_ENDPOINTS.INTEGRATIONS,
                  orgId,
                  pagination,
                  filter
               ],
               queryFn: () => IntegrationClient.searchIntegrations(payload),
               placeholderData: keepPreviousData,
               enabled: !!orgId,
            },
         )
      },
      monitorConfig,
      getHealthCheckOption: () => options,
      attemptToCreateMonitorConfig,
      attemptToUpdateMonitorConfig
   }
};
