import { ClientOnly } from 'remix-utils/client-only';
import WebhooksPage from 'sections/webhooks';
import { WebhooksSkeleton } from 'components/skeletons';
import { Paper } from '@mui/material';

export default function Webhooks() {
  return (
    <ClientOnly fallback={<WebhooksSkeleton />}>
      {() => (
        <Paper 
          elevation={2}
          sx={{ 
            p: 3,
            borderRadius: '8px',
            backgroundColor: (theme) => theme.palette.background.paper
          }}
        >
          <WebhooksPage />
        </Paper>
      )}
    </ClientOnly>
  );
}