import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import {
  Box,
  Typography,
  Button,
  Stack,
  Grid,
  Paper,
  Chip,
  TextField,
  IconButton,
  Tooltip,
  Alert,
  useTheme,
  alpha,
  CircularProgress,
  Collapse,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Checkbox,
  FormControlLabel,
  Switch,
  InputAdornment,
  Menu,
  MenuItem,
  Badge,
  Skeleton
} from '@mui/material';
import {
  Search,
  ChevronRight,
  Check,
  X,
  AlertCircle,
  GitBranch,
  GitCommit,
  Users,
  FolderGit,
  ChevronDown,
  ChevronUp,
  Link,
  Link2,
  Zap,
  Copy,
  MoreVertical,
  Loader2,
  FileText
} from 'lucide-react';
import { useQuery, useQueries, useQueryClient, useMutation } from '@tanstack/react-query';
import useGetAdditionalAttributes from 'hooks/api/additional-attributes';
import { useGetServiceProfile } from 'hooks/api/service-profile/useGetServiceProfile';
import useGetFieldMappings from 'hooks/api/use-field-mappings';
import { FieldMappings } from 'types/field-mappings';
import { parseError } from 'lib/utils';
import { toast } from 'sonner';

interface ServiceFieldMappingStepProps {
  category: string;
  categoryLabel: string;
  onBack: () => void;
  onComplete: (mappings?: Record<string, FieldMapping[]>) => void;
}

interface ServiceConfig {
  id: string;
  name: string;
  icon: React.ReactNode;
  status: 'active' | 'inactive';
  fieldCount: number;
  mappedCount: number;
}

interface FieldMapping {
  customFieldId: string;
  customFieldName: string;
  customFieldKey: string;
  customFieldModel?: string; // e.g., 'organization', 'repository', 'pull_request'
  customFieldType?: string; // e.g., 'string', 'object', 'array'
  serviceFieldPath: string;
  serviceFieldName: string;
  serviceFieldType?: string; // Type of the service field
  mapped: boolean;
  suggested?: boolean;
  hasTypeError?: boolean; // Flag for type mismatch
  parentFieldId?: string; // For child fields
  children?: FieldMapping[]; // For object/array type fields
}

interface ServiceFieldGroup {
  groupName: string;
  fields: Array<{
    path: string;
    name: string;
    type: string;
    required?: boolean;
    description?: string;
    isNested?: boolean;
    level?: number;
  }>;
}

// Model configuration with data type mapping
const modelConfig: Record<string, { name: string; icon: React.ReactNode; dataType?: string }> = {
  organization: { name: 'Organization', icon: <Users size={16} />, dataType: 'ORGANIZATION' },
  repository: { name: 'Repository', icon: <FolderGit size={16} />, dataType: 'COLLECTIONS' },
  pull_request: { name: 'Pull Request', icon: <GitBranch size={16} />, dataType: 'PULL_REQUEST' },
  branch: { name: 'Branch', icon: <GitBranch size={16} />, dataType: 'BRANCH' },
  commit: { name: 'Commit', icon: <GitCommit size={16} />, dataType: 'COMMIT' },
  ticket: { name: 'Ticket', icon: <FileText size={16} />, dataType: 'TICKET' },
  comment: { name: 'Comment', icon: <FileText size={16} />, dataType: 'COMMENT' },
  collection: { name: 'Collection', icon: <FileText size={16} />, dataType: 'COLLECTION' },
  label: { name: 'Label', icon: <FileText size={16} />, dataType: 'LABEL' },
  attachment: { name: 'Attachment', icon: <FileText size={16} />, dataType: 'ATTACHMENT' },
  user: { name: 'User', icon: <Users size={16} />, dataType: 'USER' },
};
   // Define aliases to handle connector variations (underscores vs no underscores, model unification)
    const entityAliasesByModel: Record<string, string[]> = {
      pull_request: ['PULL_REQUEST', 'PULLREQUEST', 'MERGE_REQUEST', 'MERGEREQUEST'],
      merge_request: ['PULL_REQUEST', 'PULLREQUEST', 'MERGE_REQUEST', 'MERGEREQUEST'],
      project: ['PROJECT', 'COLLECTIONS'],
      repository: ['COLLECTION', 'REPOSITORY'],
      board: ['COLLECTION', 'PROJECT', 'REPOSITORY'],
      organization: ['ORGANIZATION'],
      branch: ['BRANCH'],
      commit: ['COMMIT'],
      issue: ['ISSUE'],
      comment: ['COMMENT'],
      ticket: ['TICKET'],
      incident: ['INCIDENT'],
      user: ['USER'],
      general: ['GENERAL'],
      label: ['LABEL'],
      attachment: ['ATTACHMENT'],
      collections: ['COLLECTION', 'PROJECT', 'REPOSITORY'],
    };

const ServiceFieldMappingStep: React.FC<ServiceFieldMappingStepProps> = ({
  category,
  categoryLabel,
  onBack,
  onComplete
}) => {
  const theme = useTheme();
  const [selectedService, setSelectedService] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedGroups, setExpandedGroups] = useState<string[]>([]);
  const [mappings, setMappings] = useState<Record<string, FieldMapping[]>>({});
  const [bulkMode, setBulkMode] = useState(false);
  const [selectedFields, setSelectedFields] = useState<string[]>([]);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [expandedModelGroups, setExpandedModelGroups] = useState<string[]>(['organization', 'repository', 'pull_request', 'merge_request', 'branch', 'commit', 'general', 'ticket', 'comment', 'user','attachment','label','project','issue']);
  const [expandedFields, setExpandedFields] = useState<string[]>([]);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [modelSpecifications, setModelSpecifications] = useState<Record<string, any>>({});
  const [loadingSpecifications, setLoadingSpecifications] = useState<Record<string, boolean>>({});
  const [renderKey, setRenderKey] = useState(0); // Force re-render on service change
  const [updatingFields, setUpdatingFields] = useState<Set<string>>(new Set()); // Track fields being updated
  const mappingContentRef = useRef<HTMLDivElement>(null);
  const queryClient = useQueryClient();
  const mappingVersionRef = useRef(0);
  const pendingUpdatesRef = useRef<Map<string, any>>(new Map());
  const lastInvalidationRef = useRef<number>(0); // Track last invalidation time
  const lastFieldMappingsHashRef = useRef<string>(''); // Track field mappings changes

  // Debounced invalidation to prevent rapid successive calls (reduced delay for better responsiveness)
  const debouncedInvalidateQueries = useCallback(async (queryKey: any[], delay: number = 300) => {
    const now = Date.now();
    if (now - lastInvalidationRef.current < delay) {
      return; // Skip if called too recently
    }
    lastInvalidationRef.current = now;
    await queryClient.invalidateQueries({ queryKey });
  }, [queryClient]);

  // Fetch custom fields with error handling
  const { getAllAdditionalAttributesQuery, actions: additionalAttributesActions } = useGetAdditionalAttributes();
  const { 
    data: customFieldsData, 
    isLoading: isLoadingCustomFields,
    error: customFieldsError,
    refetch: refetchCustomFields
  } = useQuery({
    ...getAllAdditionalAttributesQuery(),
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    onError: (error: any) => {
      console.error('Failed to fetch custom fields:', error);
      const errorMessage = error?.response?.data?.message || error?.message || 'Failed to load custom fields';
      toast.error(errorMessage);
    }
  });

  // Fetch available services using search API
  const serviceProfileHook = useGetServiceProfile({
    searchOptions: {
      search: {
        type: category,
        limit: 50,
        offset: 0
      }
    }
  });

  const { serviceProfileClient } = serviceProfileHook;
  const { 
    data: services = [], 
    isLoading: isLoadingServices,
    error: servicesError,
    refetch: refetchServices
  } = serviceProfileClient;

  // Filter active services that have been enrolled
  const categoryServices = services.filter(
    service => service.service?.state === 'ACTIVE'
  );

  // Fetch service profile specifications for field groups
  const { serviceProfileSpecificationQueryOptions, serviceProfileDataTypesQueryOptions } = serviceProfileHook;
  
  const selectedServiceProfile = categoryServices.find(s => s.id === selectedService);
  
  // First get available data types for the service
  const { data: dataTypesData } = useQuery({
    ...serviceProfileDataTypesQueryOptions({
      id: selectedServiceProfile?.id || ''
    }),
    enabled: !!selectedServiceProfile?.id
  });
  // Add this useEffect to log all dataTypes and their states
  
  // Get all available data types instead of just the first one
  const availableDataTypes = dataTypesData?.data?.data || [];
  const availableDataType = availableDataTypes[0]?.key; // Keep for backward compatibility
  
  
  // Keep the original serviceSpecData for backward compatibility with existing logic
  const { data: serviceSpecData, isLoading: isLoadingSpecs } = useQuery({
    ...serviceProfileSpecificationQueryOptions({
      id: selectedServiceProfile?.id || '',
      params: { dataType: availableDataType }
    }),
    enabled: !!selectedServiceProfile?.id && !!availableDataType
  });
  
  // Helper to get connector dataType keys mapped to a model/entity (with robust alias matching)
  const getModelDataTypeKeys = useCallback((model: string): string[] => {
    const defaultEntity = (modelConfig[model]?.dataType || model.toUpperCase());

 
    const candidates = (entityAliasesByModel[model] || [defaultEntity])
      .filter(Boolean)
      .map((s) => String(s).toUpperCase());

    const keys = (availableDataTypes || [])
      .filter((dt: any) => {
        const keyUpper = String(dt?.key || '').toUpperCase();
        const entityUpper = String(dt?.mappedTo?.entity || '').toUpperCase();
        return candidates.includes(keyUpper) || candidates.includes(entityUpper);
      })
      .map((dt: any) => dt.key);

    return keys;
  }, [availableDataTypes]);

  // Get first dataType key for a given model (helper)
  const getDataTypeKeyForModel = useCallback((model: string): string | null => {
    const modelDataTypes = getModelDataTypeKeys(model);
    return modelDataTypes.length > 0 ? modelDataTypes[0] : null;
  }, [getModelDataTypeKeys]);

  // Get mappedTo.entity for a given model (preferred for dataModel.type)
  const getEntityForModel = useCallback((model: string): string | null => {
    const defaultEntity = (modelConfig[model]?.dataType || model.toUpperCase());
    const candidates = (entityAliasesByModel[model] || [defaultEntity])
      .filter(Boolean)
      .map((s) => String(s).toUpperCase());

    const match = (availableDataTypes || []).find((dt: any) => {
      const keyUpper = String(dt?.key || '').toUpperCase();
      const entityUpper = String(dt?.mappedTo?.entity || '').toUpperCase();
      return candidates.includes(keyUpper) || candidates.includes(entityUpper);
    });

    return match?.mappedTo?.entity ? String(match.mappedTo.entity).toUpperCase() : null;
  }, [availableDataTypes]);

  // Function to fetch specifications for a specific model and dataType (single call)
  const fetchModelSpecifications = async (model: string, dataType: string, forceRefresh: boolean = false) => {
    if (!selectedServiceProfile?.id || !availableDataTypes.length || !dataType) {
      
      return;
    }

    const cacheKey = `${selectedService}-${model}-${dataType}`;
    if (loadingSpecifications[cacheKey]) return;
    if (!forceRefresh && modelSpecifications[cacheKey]) return;

    setLoadingSpecifications(prev => ({ ...prev, [cacheKey]: true }));

    try {
      const queryOptions = serviceProfileSpecificationQueryOptions({
        id: selectedServiceProfile.id,
        params: { dataType }
      });
      const response = await queryOptions.queryFn();
      setModelSpecifications(prev => ({ 
        ...prev, 
        [cacheKey]: response 
      }));
    } catch (error) {
      console.error(`Failed to fetch specifications for ${selectedService} - ${model} - ${dataType}:`, error);
    } finally {
      setLoadingSpecifications(prev => ({ ...prev, [cacheKey]: false }));
    }
  };

  // Initialize field mappings hooks
  const { createFieldMappingsQuery, updateFieldMappingsQuery, getAllFieldMappings } = useGetFieldMappings();
  const createFieldMappingsMutation = useMutation(createFieldMappingsQuery());
  const updateFieldMappingsMutation = useMutation(updateFieldMappingsQuery());

  console.log('Field mappings mutations initialized:', {
    createMutation: !!createFieldMappingsMutation,
    updateMutation: !!updateFieldMappingsMutation,
    createStatus: createFieldMappingsMutation.status,
    updateStatus: updateFieldMappingsMutation.status,
    selectedServiceId: selectedServiceProfile?.service?.id
  });

  // Update field mappings query to handle multiple dataTypes - SCOPED TO SELECTED SERVICE
  const fieldMappingsQueries = useMemo(() => {
    const serviceId = selectedServiceProfile?.service?.id;
    if (!serviceId || !availableDataTypes?.length) {
      return [] as any[];
    }

    return availableDataTypes.map((dataType: any) => ({
      ...getAllFieldMappings({
        serviceId: serviceId, // This ensures each service has its own query
        params: { dataType: dataType?.mappedTo?.entity }
      }),
      enabled: !!serviceId && !!dataType?.mappedTo?.entity,
      // Add a unique query key that includes the service ID to prevent cross-service caching
      queryKey: ['fieldMappings', serviceId, dataType?.mappedTo?.entity],
      staleTime: 5 * 60 * 1000,
      cacheTime: 10 * 60 * 1000,
    }));
  }, [selectedServiceProfile?.service?.id, availableDataTypes?.length, getAllFieldMappings]);

  // Use useQueries to fetch all field mappings simultaneously
  const fieldMappingsResults = useQueries({
    queries: fieldMappingsQueries
  });

  // Combine all field mappings data with better error handling
  const allFieldMappingsData = useMemo(() => {
    const combinedData: any[] = [];
    fieldMappingsResults.forEach(result => {
      if (result.data?.data?.data) {
        combinedData.push(...result.data.data.data);
      }
    });
    console.log('Combined field mappings data:', combinedData);
    return combinedData;
  }, [fieldMappingsResults.map(r => r.data).join(',')]);

  // Track loading state for field mappings
  const isFieldMappingsLoading = fieldMappingsResults.some(result => result.isLoading);
  const hasFieldMappingsError = fieldMappingsResults.some(result => result.error);

  // Get existing field mappings organized by data model type
  const existingFieldMappingsByModel = useMemo(() => {
    const mappingsByModel: Record<string, any> = {};
    allFieldMappingsData.forEach(mapping => {
      if (mapping?.dataModel?.type) {
        mappingsByModel[mapping.dataModel.type.toUpperCase()] = mapping;
      }
    });
    console.log('Field mappings by model updated:', mappingsByModel);
    return mappingsByModel;
  }, [allFieldMappingsData]);

  // Simplified re-sync when field mappings data changes
  useEffect(() => {
    if (selectedService && selectedServiceProfile?.service?.id && !isFieldMappingsLoading) {
      const currentServiceId = selectedServiceProfile.service.id;
      const loadedKey = `${currentServiceId}-mappings-loaded`;

      // Always clear the loaded flag when field mappings data changes to ensure fresh loading
      setSuggestionsApplied(prev => {
        const newSet = new Set(prev);
        newSet.delete(loadedKey);
        return newSet;
      });
    }
  }, [selectedService, selectedServiceProfile?.service?.id, allFieldMappingsData.length, isFieldMappingsLoading]);

  // Helper function to build field mappings payload for API (optionally filter by model)
  const buildFieldMappingsPayload = (
    fieldMappings: FieldMapping[],
    targetModel?: string,
    isUpdate: boolean = false
  ): FieldMappings.Mappings => {
    const mappingPayload: FieldMappings.Mappings = {};
    const processedKeys = new Set<string>(); // Track processed keys to prevent duplicates

    const processMapping = (
      mapping: FieldMapping,
      parentPath: string = ''
    ) => {
      // Skip unmapped fields
      if (!mapping.mapped || !mapping.serviceFieldPath || !mapping.serviceFieldName) {
        // Still process children even if parent is not mapped
        if (mapping.children && mapping.children.length > 0) {
          mapping.children.forEach(child => {
            processMapping(child, parentPath ? `${parentPath}.${mapping.customFieldKey}` : mapping.customFieldKey);
          });
        }
        return;
      }

      // Filter by model if provided
      if (targetModel && (mapping.customFieldModel || 'general') !== targetModel) {
        // Still process children even if parent doesn't match model
        if (mapping.children && mapping.children.length > 0) {
          mapping.children.forEach(child => {
            processMapping(child, parentPath ? `${parentPath}.${mapping.customFieldKey}` : mapping.customFieldKey);
          });
        }
        return;
      }

      const fieldPath = parentPath
        ? `${parentPath}.${mapping.customFieldKey}`
        : mapping.customFieldKey;

      // For updates, allow overwriting existing keys instead of skipping them
      if (!isUpdate && processedKeys.has(fieldPath)) {
        console.warn(`Duplicate mapping key detected: ${fieldPath}. Skipping.`);
        return;
      }

      processedKeys.add(fieldPath);

      // Use the correct payload structure for field mappings API
      mappingPayload[fieldPath] = {
        key: mapping.customFieldKey,
        type: parentPath ? 'child' as const : 'root' as const,
        isPredefined: false,
        source: {
          field: mapping.serviceFieldName,
          type: mapping.serviceFieldType || 'string',
          path: mapping.serviceFieldPath
        },
        target: {
          field: mapping.customFieldKey,
          type: mapping.customFieldType || 'string',
          path: fieldPath
        }
      };

      // Process children recursively
      if (mapping.children && mapping.children.length > 0) {
        mapping.children.forEach(child => {
          processMapping(child, fieldPath);
        });
      }
    };

    fieldMappings.forEach(mapping => processMapping(mapping));

    console.log('Built field mappings payload:', {
      targetModel,
      isUpdate,
      inputMappings: fieldMappings.filter(m => m.mapped),
      outputPayload: mappingPayload
    });

    return mappingPayload;
  };

  // Get service field groups from API or use mock data
  const getServiceFieldGroups = useMemo(() => (serviceId: string, model?: string): ServiceFieldGroup[] => {
    // Try to find specifications for this model across all dataTypes
    const modelSpecsForAllDataTypes: any[] = [];
    
    availableDataTypes.forEach((dataType: any) => {
      const cacheKey = model ? `${serviceId}-${model}-${dataType.key}` : null;
      
      if (cacheKey && modelSpecifications[cacheKey]?.data?.data) {
        modelSpecsForAllDataTypes.push({
          dataTypeKey: dataType.key,
          dataTypeName: dataType.name,
          specs: modelSpecifications[cacheKey].data.data
        });
      }
    });
    
    // If we have model-specific data from any dataType, use it
    if (modelSpecsForAllDataTypes.length > 0) {
      const groups: ServiceFieldGroup[] = [];
      const fieldsByGroup: Record<string, any[]> = {};
      
      // Combine fields from all dataTypes for this model
      modelSpecsForAllDataTypes.forEach(({ dataTypeKey, dataTypeName, specs }) => {
        specs.forEach((specGroup: any) => {
          if (specGroup.fields) {
            specGroup.fields.forEach((spec: any) => {
              const pathParts = spec.name?.split('.') || [];
              const groupName = pathParts.length > 1 ? pathParts[0] : `${dataTypeName} Fields`;
              
              if (!fieldsByGroup[groupName]) {
                fieldsByGroup[groupName] = [];
              }
              
              // Add dataType info to field for better identification
              fieldsByGroup[groupName].push({
                path: spec.name,
                name: spec.displayName || spec.name,
                type: spec.type || 'string',
                required: spec.required || false,
                description: spec.description,
                dataType: dataTypeKey,
                dataTypeName: dataTypeName
              });
            });
          }
        });
      });
      
      // Convert to groups array
      Object.entries(fieldsByGroup).forEach(([groupName, fields]) => {
        groups.push({
          groupName: groupName.charAt(0).toUpperCase() + groupName.slice(1),
          fields: fields
        });
      });
      
      return groups;
    }
    
    // If we have general serviceSpecData, use it as fallback
    if (serviceSpecData?.data?.data && serviceSpecData.data.data.length > 0) {
      const specs = serviceSpecData.data.data[0]?.fields;
      const groups: ServiceFieldGroup[] = [];
      
      // Group fields by their parent path
      const fieldsByGroup: Record<string, any[]> = {};
      
      specs.forEach((spec: any) => {
        const pathParts = spec.name?.split('.') || [];
        const groupName = pathParts.length > 1 ? pathParts[0] : 'Basic Fields';
        
        if (!fieldsByGroup[groupName]) {
          fieldsByGroup[groupName] = [];
        }
        
        fieldsByGroup[groupName].push({
          path: spec.name,
          name: spec.displayName || spec.name,
          type: spec.type || 'string',
          required: spec.required || false,
          description: spec.description
        });
      });
      
      // Convert to groups array
      Object.entries(fieldsByGroup).forEach(([groupName, fields]) => {
        groups.push({
          groupName: groupName.charAt(0).toUpperCase() + groupName.slice(1),
          fields: fields
        });
      });
      
      return groups;
    }
    

    return [];
  }, [modelSpecifications, availableDataTypes, serviceSpecData, category, selectedService]);

  // Track if mappings have been initialized to prevent infinite loops
  const [mappingsInitialized, setMappingsInitialized] = useState(false);
  // Track which services have had suggestions applied
  const [suggestionsApplied, setSuggestionsApplied] = useState<Set<string>>(new Set());
  
  // Reset initialization when category changes
  useEffect(() => {
    setMappingsInitialized(false);
    setSuggestionsApplied(new Set());
    setMappings({});
    setSelectedService(null);
    setExpandedModelGroups(['organization', 'repository', 'pull_request','merge_request', 'branch', 'commit', 'general','collections','ticket','comment','user','label','project','issue','attachment']);
    setExpandedFields([]);
    lastFieldMappingsHashRef.current = ''; // Reset the hash

    // Force refetch of all field mappings when category changes
    queryClient.invalidateQueries({
      queryKey: ['fieldMappings']
    });
  }, [category, queryClient]);

  // Clear cache when selected service changes
  useEffect(() => {
    // Clear cache only for the previous service to optimize performance
    if (selectedService) {
      setModelSpecifications(prev => {
        const newSpecs = { ...prev };
        // Clear only the previous service's cache
        Object.keys(newSpecs).forEach(key => {
          if (key.startsWith(selectedService)) {
            delete newSpecs[key];
          }
        });
        return newSpecs;
      });
    }
    setLoadingSpecifications({});
    // Keep model groups expanded for better UX
    setExpandedModelGroups(['organization', 'repository', 'board', 'pull_request','merge_request', 'branch', 'commit', 'general', 'ticket', 'comment', 'user','label','project','issue','attachment']);

    // Force re-render by incrementing key
    setRenderKey(prev => prev + 1);

    // Only invalidate service profile specifications, not all service profile data
    if (selectedService) {
      queryClient.removeQueries({
        queryKey: ['serviceProfile', 'specifications', selectedService]
      });
    }
  }, [selectedService, queryClient]);

  // Simplified field mappings loading logic with better data synchronization
  useEffect(() => {
    if (!selectedService || !selectedServiceProfile?.service?.id || !mappingsInitialized || isFieldMappingsLoading) {
      return;
    }

    const currentServiceId = selectedServiceProfile.service.id;
    const loadedKey = `${currentServiceId}-mappings-loaded`;

    // Only proceed if we have both mappings and field mappings data
    const currentMappings = mappings[selectedService];
    if (!currentMappings || currentMappings.length === 0) {
      return;
    }

    // Check if we have any field mappings data to work with
    const hasFieldMappingsData = Object.keys(existingFieldMappingsByModel).length > 0;
    if (!hasFieldMappingsData) {
      return;
    }

    // Always reload mappings when field mappings data changes (don't skip based on loaded flag)
    console.log('Loading existing field mappings for service:', serviceId, existingFieldMappingsByModel);

    // Update mappings with existing data from all models
    setMappings(prev => {
      // Helper to update mapping with existing data
      const updateWithExistingData = (mappings: FieldMapping[], parentPath: string = ''): FieldMapping[] => {
        return mappings.map(mapping => {
          // Construct the full path for this field
          const fieldPath = parentPath ? `${parentPath}.${mapping.customFieldKey}` : mapping.customFieldKey;

          // Get the data model type for this field
          const fieldModel = mapping.customFieldModel || 'general';
          const entityForModel = getEntityForModel(fieldModel) || getDataTypeKeyForModel(fieldModel);

          // Find the appropriate existing field mapping for this model
          const existingFieldMappingForModel = entityForModel ? existingFieldMappingsByModel[entityForModel.toUpperCase()] : null;

          if (existingFieldMappingForModel?.mappings) {
            const existingMappings = existingFieldMappingForModel.mappings;

            // Find existing mapping by path
            const existingMap = existingMappings[fieldPath] as FieldMappings.MappingValue | undefined;

            if (existingMap) {
              const updatedMapping: FieldMapping = {
                ...mapping,
                serviceFieldPath: existingMap.source.path,
                serviceFieldName: existingMap.source.field,
                serviceFieldType: existingMap.source.type,
                mapped: true,
                suggested: false,
                hasTypeError: !!(mapping.customFieldType && existingMap.source.type &&
                  !areTypesCompatible(mapping.customFieldType, existingMap.source.type))
              };

              // Process children if any
              if (mapping.children && mapping.children.length > 0) {
                updatedMapping.children = updateWithExistingData(mapping.children, fieldPath);
              }

              return updatedMapping;
            }
          }

          // Process children even if parent is not mapped
          if (mapping.children && mapping.children.length > 0) {
            return {
              ...mapping,
              children: updateWithExistingData(mapping.children, fieldPath)
            };
          }

          return mapping;
        });
      };

      return {
        ...prev,
        [selectedService]: updateWithExistingData(currentMappings)
      };
    });

    // Mark mappings as loaded for this specific service (but allow reloading when data changes)
    setSuggestionsApplied(prev => new Set(prev).add(loadedKey));
  }, [selectedService, selectedServiceProfile?.service?.id, mappingsInitialized, allFieldMappingsData.length, isFieldMappingsLoading, JSON.stringify(existingFieldMappingsByModel)]);

  // Clear field mappings data when service changes and force fresh data
  useEffect(() => {
    if (selectedService && selectedServiceProfile?.service?.id) {
      const currentServiceId = selectedServiceProfile.service.id;

      // Clear the suggestions applied flag for the new service to force reload
      setSuggestionsApplied(prev => {
        const newSet = new Set(prev);
        const loadedKey = `${currentServiceId}-mappings-loaded`;
        newSet.delete(loadedKey);
        return newSet;
      });

      // Invalidate and refetch field mappings immediately for the new service
      queryClient.invalidateQueries({
        queryKey: ['fieldMappings', currentServiceId]
      });

      // Force immediate refetch without throttling when service changes
      fieldMappingsResults.forEach(result => {
        if (result.refetch && !result.isLoading) {
          result.refetch();
        }
      });
    }
  }, [selectedService, selectedServiceProfile?.service?.id]);

  // Handle online/offline detection
  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      toast.success('Connection restored');
      // Refetch data when coming back online
      queryClient.invalidateQueries();
    };

    const handleOffline = () => {
      setIsOnline(false);
      toast.error('Connection lost. Changes may not be saved.');
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [queryClient]);

  // Initialize mappings with prepopulated suggestions
  useEffect(() => {
    // Only initialize once when we have all the required data
    if (!mappingsInitialized && customFieldsData?.data?.data && categoryServices.length > 0) {
      const customFields = customFieldsData.data.data.filter(
        field => field.category?.type === category
      );

      const initialMappings: Record<string, FieldMapping[]> = {};

      // Helper function to create field mappings including children
      const createFieldMapping = (field: any, parentId?: string): FieldMapping => {
        const mapping: FieldMapping = {
          customFieldId: field.id,
          customFieldName: field.name,
          customFieldKey: field.key || field.name,
          customFieldModel: field.dataModel?.type || 'general',
          customFieldType: field.dataType?.type || field.type || 'string',
          serviceFieldPath: '',
          serviceFieldName: '',
          mapped: false,
          suggested: false,
          parentFieldId: parentId
        };

        // If field has children (object/array type), recursively create mappings
        if (field.children && field.children.length > 0) {
          mapping.children = field.children.map((child: any) => 
            createFieldMapping(child, field.id)
          );
        }

        return mapping;
      };

      categoryServices.forEach(service => {
        const serviceMappings: FieldMapping[] = [];

        customFields.forEach(customField => {
          // Create mapping including any children
          serviceMappings.push(createFieldMapping(customField));
        });

        initialMappings[service.id] = serviceMappings;
      });

      setMappings(initialMappings);
      setMappingsInitialized(true);
      
      // Auto-expand all object fields by default
      const objectFieldIds: string[] = [];
      const collectObjectFieldIds = (mappings: FieldMapping[]) => {
        mappings.forEach(mapping => {
          if ((mapping.customFieldType === 'object' || mapping.customFieldType === 'array') && 
              mapping.children && mapping.children.length > 0) {
            objectFieldIds.push(mapping.customFieldId);
          }
          if (mapping.children) {
            collectObjectFieldIds(mapping.children);
          }
        });
      };
      
      Object.values(initialMappings).forEach(serviceMappings => {
        collectObjectFieldIds(serviceMappings);
      });
      
      setExpandedFields(objectFieldIds);
      
      // Select the first service by default
      if (categoryServices.length > 0 && !selectedService) {
        setSelectedService(categoryServices[0].id);
      }
    }
  }, [mappingsInitialized, customFieldsData, categoryServices.length, category]);


  // Auto-suggest mappings when a service is selected and we have field specs
  useEffect(() => {
    if (selectedService &&
        serviceSpecData?.data?.data &&
        mappings[selectedService] &&
        !suggestionsApplied.has(selectedService) &&
        Object.keys(existingFieldMappingsByModel).length === 0) { // Don't auto-suggest if we have existing mappings
      
      const serviceFieldGroups = getServiceFieldGroups(selectedService);
      const currentMappings = mappings[selectedService];
      let hasChanges = false;
      
      const updatedMappings = currentMappings.map(mapping => {
        // Skip if already mapped
        if (mapping.mapped) return mapping;
        
        let suggestedMapping = null;
        
        serviceFieldGroups.forEach(group => {
          group.fields.forEach(serviceField => {
            const customKey = mapping.customFieldKey.toLowerCase();
            const serviceKey = serviceField.name.toLowerCase().replace(/\s+/g, '_');
            
            // Simple matching logic with type compatibility check
            if ((customKey === serviceKey || 
                customKey.includes(serviceKey) || 
                serviceKey.includes(customKey)) &&
                (!mapping.customFieldType || areTypesCompatible(mapping.customFieldType, serviceField.type))) {
              suggestedMapping = {
                path: serviceField.path,
                name: serviceField.name,
                type: serviceField.type
              };
            }
          });
        });
        
        if (suggestedMapping && !mapping.mapped) {
          hasChanges = true;
          return {
            ...mapping,
            serviceFieldPath: suggestedMapping.path,
            serviceFieldName: suggestedMapping.name,
            serviceFieldType: suggestedMapping.type,
            mapped: true,
            suggested: true,
            hasTypeError: false // Auto-suggestions are type-compatible
          };
        }
        
        return mapping;
      });
      
      if (hasChanges) {
        setMappings(prev => ({
          ...prev,
          [selectedService]: updatedMappings
        }));
      }
      
      // Mark this service as having suggestions applied
      setSuggestionsApplied(prev => new Set(prev).add(selectedService));
    }
  }, [selectedService, serviceSpecData?.data?.data?.length, mappings[selectedService]?.length, Object.keys(existingFieldMappingsByModel).length]);

  // Auto-fetch specifications for expanded model groups to improve UX (only first missing dataType per model)
  useEffect(() => {
    if (!selectedService || !availableDataTypes.length) return;
    
    expandedModelGroups.forEach(model => {
      const keys = getModelDataTypeKeys(model);
      const firstMissing = keys.find(dtKey => {
        const cacheKey = `${selectedService}-${model}-${dtKey}`;
        return !modelSpecifications[cacheKey] && !loadingSpecifications[cacheKey];
      });
      if (firstMissing) {
        fetchModelSpecifications(model, firstMissing);
      }
    });
  }, [expandedModelGroups, selectedService, availableDataTypes.length, getModelDataTypeKeys]);

  const handleFieldMapping = async (serviceId: string, customFieldId: string, serviceFieldPath: string, serviceFieldName: string, serviceFieldType?: string) => {
    // Create a unique update ID to track this specific update
    const updateId = `${serviceId}-${customFieldId}-${Date.now()}`;

    console.log('handleFieldMapping called with:', {
      serviceId,
      customFieldId,
      serviceFieldPath,
      serviceFieldName,
      serviceFieldType,
      updateId,
      selectedServiceProfile: selectedServiceProfile?.service?.id,
      createMutationStatus: createFieldMappingsMutation.status,
      updateMutationStatus: updateFieldMappingsMutation.status
    });

    try {
      // Validate inputs
      if (!serviceId || !customFieldId) {
        console.error('Invalid field mapping parameters:', { serviceId, customFieldId });
        toast.error('Invalid field mapping parameters');
        return;
      }

      // Check if there's already a pending update for this field
      const pendingKey = `${serviceId}-${customFieldId}`;
      if (pendingUpdatesRef.current.has(pendingKey)) {
        console.warn('Another update is in progress for this field:', pendingKey);
        toast.warning('Another update is in progress for this field. Please wait.');
        return;
      }

      // Mark this update as pending
      pendingUpdatesRef.current.set(pendingKey, updateId);
      console.log('Marked update as pending:', { pendingKey, updateId });

      // Add to updating fields set for loading state
      setUpdatingFields(prev => new Set(prev).add(customFieldId));

      // Optimistically update the UI
      setMappings(prev => {
        // Helper function to recursively update nested fields
        const updateFieldRecursively = (mappings: FieldMapping[]): FieldMapping[] => {
          return mappings.map(mapping => {
            if (mapping.customFieldId === customFieldId) {
              const hasTypeError = Boolean(
                serviceFieldType &&
                mapping.customFieldType &&
                !areTypesCompatible(mapping.customFieldType, serviceFieldType)
              );
              
              return { 
                ...mapping, 
                serviceFieldPath, 
                serviceFieldName, 
                serviceFieldType,
                mapped: !!serviceFieldPath,
                suggested: false, // Clear suggested flag when manually mapped
                hasTypeError
              };
            }
            
            // If this mapping has children, recursively update them
            if (mapping.children && mapping.children.length > 0) {
              return {
                ...mapping,
                children: updateFieldRecursively(mapping.children)
              };
            }
            
            return mapping;
          });
        };
        
        return {
          ...prev,
          [serviceId]: updateFieldRecursively(prev[serviceId])
        };
      });

      // Get the updated mappings for this service
      const updatedMappings = mappings[serviceId] || [];
      
      // Helper to update mappings with current state
      const getMappingsWithUpdate = () => {
        const updateFieldRecursively = (mappings: FieldMapping[]): FieldMapping[] => {
          return mappings.map(mapping => {
            if (mapping.customFieldId === customFieldId) {
              const hasTypeError = Boolean(
                serviceFieldType &&
                mapping.customFieldType &&
                !areTypesCompatible(mapping.customFieldType, serviceFieldType)
              );
              
              return { 
                ...mapping, 
                serviceFieldPath, 
                serviceFieldName, 
                serviceFieldType,
                mapped: !!serviceFieldPath,
                suggested: false,
                hasTypeError
              };
            }
            
            if (mapping.children && mapping.children.length > 0) {
              return {
                ...mapping,
                children: updateFieldRecursively(mapping.children)
              };
            }
            
            return mapping;
          });
        };
        
        return updateFieldRecursively(updatedMappings);
      };

      const mappingsToSave = getMappingsWithUpdate();
      // Determine target model for this field
      const serviceMappingsNow = mappings[serviceId] || [];
      const findField = (arr: FieldMapping[]): FieldMapping | null => {
        for (const m of arr) {
          if (m.customFieldId === customFieldId) return m;
          if (m.children) {
            const f = findField(m.children);
            if (f) return f;
          }
        }
        return null;
      };
      const targetField = findField(serviceMappingsNow);
      const targetModel = targetField?.customFieldModel || 'general';
      console.log(targetField,"targetField");

      // Only proceed if we have a selected service profile
      if (!selectedServiceProfile?.service?.id) {
        toast.error('No service selected');
        return;
      }

      // Decide entity for the model (mappedTo.entity preferred)
      const entityForModel = getEntityForModel(targetModel) || getDataTypeKeyForModel(targetModel);
      if (!entityForModel) {
        toast.error(`No entity/dataType found for model: ${targetModel}`);
        return;
      }

      // Create or update per-model mapping keyed by entity
      const existingMappingForModel = allFieldMappingsData.find(m => String(m?.dataModel?.type || '').toUpperCase() === String(entityForModel).toUpperCase());

      // Build payload for only the specific model of this field
      const isUpdate = !!existingMappingForModel?.id;
      const mappingPayload = buildFieldMappingsPayload(mappingsToSave, targetModel, isUpdate);

      console.log('Field mapping operation:', {
        isUpdate,
        targetModel,
        entityForModel,
        mappingPayload,
        mappingsToSave: mappingsToSave.filter(m => m.mapped),
        existingMappingForModel,
        hasExistingMapping: !!existingMappingForModel?.id,
        allFieldMappingsData: allFieldMappingsData.length
      });

      // Validate that we have a valid payload
      if (!mappingPayload || Object.keys(mappingPayload).length === 0) {
        console.error('No valid mapping payload generated');
        throw new Error('No valid mapping payload generated');
      }

      // Get selectedServiceId early to avoid initialization issues
      const selectedServiceId = selectedServiceProfile.service.id;

      if (!existingMappingForModel?.id) {
        // Create new mapping
        const payload: FieldMappings.CreateFieldMappingsPayload = {
          mappings: mappingPayload,
          dataModel: { type: entityForModel }
        };

        console.log('Creating new field mapping:', {
          payload,
          serviceId: selectedServiceId,
          mutationStatus: createFieldMappingsMutation.status,
          isLoading: createFieldMappingsMutation.isPending
        });

        try {
          const result = await createFieldMappingsMutation.mutateAsync({
            serviceId: selectedServiceId,
            payload
          });
          console.log('Create field mapping result:', result);
        } catch (error) {
          console.error('Create field mapping error:', error);
          throw error;
        }
      } else {
        // For updates, replace only the specific fields being updated
        const existingMappings = existingMappingForModel.mappings || {};

        // Create a new mappings object with updates
        const updatedMappings = { ...existingMappings };

        // Add or update the new mappings (this allows overwriting existing field mappings)
        Object.keys(mappingPayload).forEach(key => {
          updatedMappings[key] = mappingPayload[key];
        });

        const payload: FieldMappings.UpdateFieldMappingsPayload[] = [{ op: 'replace', path: '/mappings', value: updatedMappings }];

        console.log('Updating existing field mapping:', {
          id: existingMappingForModel.id,
          existingMappings,
          newMappings: mappingPayload,
          updatedMappings,
          payload,
          serviceId: selectedServiceId,
          mutationStatus: updateFieldMappingsMutation.status,
          isLoading: updateFieldMappingsMutation.isPending
        });

        try {
          const result = await updateFieldMappingsMutation.mutateAsync({
            serviceId: selectedServiceId,
            fieldMappingId: existingMappingForModel.id,
            payload
          });
          console.log('Update field mapping result:', result);
        } catch (error) {
          console.error('Update field mapping error:', error);
          throw error;
        }
      }

      // Comprehensive cache invalidation to ensure fresh data

      // Invalidate all field mapping related queries
      await Promise.all([
        queryClient.invalidateQueries({ queryKey: ['fieldMappings', selectedServiceId, entityForModel] }),
        queryClient.invalidateQueries({ queryKey: ['fieldMappings', selectedServiceId] }),
        queryClient.invalidateQueries({ queryKey: ['field-mappings-gat-all'] }),
        queryClient.invalidateQueries({ queryKey: ['fieldMappings'] })
      ]);

      // Reset the suggestions applied flag immediately to allow re-loading of mappings
      setSuggestionsApplied(prev => {
        const newSet = new Set(prev);
        const loadedKey = `${selectedServiceId}-mappings-loaded`;
        newSet.delete(loadedKey);
        return newSet;
      });

      // Force refetch of field mappings to get fresh data immediately
      setTimeout(() => {
        fieldMappingsResults.forEach(result => {
          if (result.refetch && !result.isLoading) {
            result.refetch();
          }
        });
      }, 100);
      
    } catch (error: any) {
      console.error('Failed to update field mapping:', error);

      // Only rollback if this is still the latest update
      if (pendingUpdatesRef.current.get(`${serviceId}-${customFieldId}`) === updateId) {
        setMappings(prev => ({
          ...prev,
          [serviceId]: mappings[serviceId] // Restore previous state
        }));
      }

      // Handle specific error cases
      const errorData = error?.response?.data;
      let errorMessage = 'Failed to update field mapping';

      if (errorData?.message) {
        errorMessage = errorData.message;
      } else if (errorData?.error) {
        errorMessage = errorData.error;
      } else if (error?.message) {
        errorMessage = error.message;
      }

      // Handle specific error types
      if (errorMessage.toLowerCase().includes('already exists') ||
          errorMessage.toLowerCase().includes('duplicate')) {
        errorMessage = 'This field mapping already exists. Please try refreshing the page and try again.';

        // Only invalidate specific service data on conflict errors
        if (selectedServiceProfile?.service?.id) {
          queryClient.invalidateQueries({
            queryKey: ['fieldMappings', selectedServiceProfile.service.id]
          });
        }
      }

      toast.error(errorMessage);
    } finally {
      // Remove from pending updates
      const pendingKey = `${serviceId}-${customFieldId}`;
      if (pendingUpdatesRef.current.get(pendingKey) === updateId) {
        pendingUpdatesRef.current.delete(pendingKey);
        console.log('Cleaned up pending update:', pendingKey);
      }

      // Remove from updating fields set
      setUpdatingFields(prev => {
        const newSet = new Set(prev);
        newSet.delete(customFieldId);
        return newSet;
      });
    }
  };

  const handleBulkAction = (action: 'map' | 'unmap') => {
    if (!selectedService || selectedFields.length === 0) return;

    if (action === 'unmap') {
      setMappings(prev => {
        // Helper function to recursively unmap selected fields
        const unmapSelectedFields = (mappings: FieldMapping[]): FieldMapping[] => {
          return mappings.map(mapping => {
            if (selectedFields.includes(mapping.customFieldId)) {
              return { 
                ...mapping, 
                serviceFieldPath: '', 
                serviceFieldName: '', 
                mapped: false,
                suggested: false
              };
            }
            
            // If this mapping has children, recursively update them
            if (mapping.children && mapping.children.length > 0) {
              return {
                ...mapping,
                children: unmapSelectedFields(mapping.children)
              };
            }
            
            return mapping;
          });
        };
        
        return {
          ...prev,
          [selectedService]: unmapSelectedFields(prev[selectedService])
        };
      });
    }

    setSelectedFields([]);
    setBulkMode(false);
  };

  // Bulk save: create/update mappings per model separately
  // const handleBulkSaveAllMappings = useCallback(async () => {
  //   if (!selectedService || !selectedServiceProfile?.service?.id) return;
  //   try {
  //     const serviceMappings = mappings[selectedService] || [];
  //     const mappingsByModel = serviceMappings
  //       .filter(m => !m.parentFieldId)
  //       .reduce((acc, m) => {
  //         const model = m.customFieldModel || 'general';
  //         if (!acc[model]) acc[model] = [] as FieldMapping[];
  //         acc[model].push(m);
  //         return acc;
  //       }, {} as Record<string, FieldMapping[]>);

  //     const tasks = Object.entries(mappingsByModel).map(async ([model, modelMappings]) => {
  //       const dataTypeKey = getDataTypeKeyForModel(model);
  //       if (!dataTypeKey) return;
  //       const mappingPayload = buildFieldMappingsPayload(modelMappings, model);
  //       const existingMappingForModel = allFieldMappingsData.find(m => m?.dataModel?.type === dataTypeKey);
  //       if (!existingMappingForModel?.id) {
  //         const payload: FieldMappings.CreateFieldMappingsPayload = { mappings: mappingPayload, dataModel: { type: dataTypeKey } };
  //         return createFieldMappings({ serviceId: selectedServiceProfile.service.id, payload });
  //       } else {
  //         const payload: FieldMappings.UpdateFieldMappingsPayload[] = [{ op: 'replace', path: '/mappings', value: mappingPayload }];
  //         return updateFieldMappings({ serviceId: selectedServiceProfile.service.id, fieldMappingId: existingMappingForModel.id, payload });
  //       }
  //     });

  //     await Promise.all(tasks);
  //     toast.success('All field mappings saved successfully');
  //   } catch (e) {
  //     console.error('Failed to save mappings:', e);
  //     toast.error('Failed to save some field mappings');
  //   }
  // }, [selectedService, selectedServiceProfile?.service?.id, mappings[selectedService], allFieldMappingsData, buildFieldMappingsPayload, getDataTypeKeyForModel, createFieldMappings, updateFieldMappings]);

  const toggleGroupExpansion = (groupName: string) => {
    setExpandedGroups(prev =>
      prev.includes(groupName)
        ? prev.filter(g => g !== groupName)
        : [...prev, groupName]
    );
  };

  // Helper function to count mapped fields recursively
  const countMappedFields = (mappings: FieldMapping[]): { total: number; mapped: number } => {
    let total = 0;
    let mapped = 0;
    
    const countRecursively = (fieldMappings: FieldMapping[]) => {
      fieldMappings.forEach(mapping => {
        total++;
        if (mapping.mapped) mapped++;
        
        if (mapping.children && mapping.children.length > 0) {
          countRecursively(mapping.children);
        }
      });
    };
    
    countRecursively(mappings);
    return { total, mapped };
  };

  // Helper function to get connector-specific model name from dataTypes
  const getConnectorModelInfo = (unizoModel: string) => {
    if (!availableDataTypes.length) return null;

    const entityAliasesByModel: Record<string, string[]> = {
      organization: ['ORGANIZATION'],
      repository: ['COLLECTION', 'COLLECTIONS', 'REPOSITORY'],
      board: ['COLLECTION', 'COLLECTIONS', 'PROJECT', 'REPOSITORY'],
      collections: ['COLLECTION', 'COLLECTIONS', 'PROJECT', 'REPOSITORY'],
      project: ['PROJECT', 'COLLECTIONS'],
      pull_request: ['PULL_REQUEST', 'PULLREQUEST', 'MERGE_REQUEST', 'MERGEREQUEST'],
      merge_request: ['PULL_REQUEST', 'PULLREQUEST', 'MERGE_REQUEST', 'MERGEREQUEST'],
      branch: ['BRANCH'],
      commit: ['COMMIT'],
      issue: ['ISSUE'],
      comment: ['COMMENT'],
      ticket: ['TICKET'],
      incident: ['INCIDENT'],
      general: ['GENERAL'],
      user: ['USER'],
      label: ['LABEL'],
      attachment: ['ATTACHMENT'],
    };

    const candidates = (entityAliasesByModel[unizoModel] || [unizoModel.toUpperCase()])
      .filter(Boolean)
      .map((s) => String(s).toUpperCase());

    const matchingDataTypes = availableDataTypes.filter((dt: any) => {
      const keyUpper = String(dt?.key || '').toUpperCase();
      const entityUpper = String(dt?.mappedTo?.entity || '').toUpperCase();
      return candidates.includes(keyUpper) || candidates.includes(entityUpper);
    });

    return matchingDataTypes.length > 0 ? matchingDataTypes : null;
  };

  // Helper function to check data type compatibility
  const areTypesCompatible = (customType: string, serviceType: string): boolean => {
    // Direct match
    if (customType === serviceType) return true;
    
    // Compatible type mappings
    const compatibleTypes: Record<string, string[]> = {
      'string': ['string', 'text', 'varchar', 'char'],
      'number': ['number', 'int', 'integer', 'float', 'double', 'decimal'],
      'boolean': ['boolean', 'bool'],
      'date': ['date', 'datetime', 'timestamp'],
      'array': ['array', 'list'],
      'object': ['object', 'json', 'jsonb']
    };
    
    // Check if types are compatible
    for (const [type, compatible] of Object.entries(compatibleTypes)) {
      if (compatible.includes(customType.toLowerCase()) && compatible.includes(serviceType.toLowerCase())) {
        return true;
      }
    }
    
    return false;
  };

  const renderServiceCard = (service: any) => {
    const serviceMappings = mappings[service.id] || [];
    const { total: totalCount, mapped: mappedCount } = countMappedFields(serviceMappings);

    // Use service image from API response
    const serviceIcon = service.image?.small || service.image?.medium || service.image?.xSmall;

    return (
      <Paper
        key={service.id}
        elevation={0}
        onClick={() => {
          setSelectedService(service.id);
          // Scroll to top of mapping content
          setTimeout(() => {
            mappingContentRef.current?.scrollTo({ top: 0, behavior: 'smooth' });
          }, 100);
        }}
        sx={{
          p: 1.5,
          cursor: 'pointer',
          border: `1.5px solid ${
            selectedService === service.id
              ? theme.palette.primary.main
              : theme.palette.divider
          }`,
          borderRadius: 1,
          transition: 'all 0.2s ease',
          backgroundColor: selectedService === service.id
            ? alpha(theme.palette.primary.main, 0.04)
            : theme.palette.background.paper,
          '&:hover': {
            borderColor: theme.palette.primary.main,
            backgroundColor: alpha(theme.palette.primary.main, 0.02),
            boxShadow: theme.shadows[1]
          }
        }}
      >
        <Stack direction="row" alignItems="center" justifyContent="space-between" spacing={1.5}>
          <Stack direction="row" alignItems="center" spacing={1.5} sx={{ flex: 1, minWidth: 0 }}>
            {serviceIcon ? (
              <Box
                component="img"
                src={serviceIcon}
                alt={service.name}
                sx={{
                  width: 32,
                  height: 32,
                  objectFit: 'contain',
                  flexShrink: 0
                }}
              />
            ) : (
              <Box
                sx={{
                  width: 32,
                  height: 32,
                  borderRadius: 0.5,
                  backgroundColor: alpha(theme.palette.primary.main, 0.1),
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  flexShrink: 0
                }}
              >
                <GitBranch size={18} color={theme.palette.primary.main} />
              </Box>
            )}
            <Tooltip 
              title={service.displayName || service.name} 
              placement="top"
              arrow
              enterDelay={500}
              disableHoverListener={false}
            >
              <Typography 
                variant="h6" 
                fontWeight={600} 
                sx={{ 
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                  cursor: 'inherit',
                  flex: 1,
                  minWidth: 0
                }}
              >
                {service.displayName || service.name}
              </Typography>
            </Tooltip>
          </Stack>
          
          <Stack direction="row" alignItems="center" spacing={1} sx={{ flexShrink: 0 }}>
            <Box sx={{ textAlign: 'right' }}>
              <Typography 
                variant="caption" 
                color={mappedCount === 0 ? 'text.secondary' : 'success.main'}
                sx={{ 
                  fontWeight: 600,
                  fontSize: '0.75rem',
                  display: 'block'
                }}
              >
                {mappedCount} mapped
              </Typography>
              <Typography 
                variant="caption" 
                color={totalCount - mappedCount > 0 ? 'warning.main' : 'text.secondary'}
                sx={{ 
                  fontSize: '0.7rem',
                  display: 'block'
                }}
              >
                {totalCount - mappedCount} unmapped
              </Typography>
            </Box>
            <ChevronRight size={16} color={theme.palette.text.secondary} />
          </Stack>
        </Stack>
      </Paper>
    );
  };

  const renderMappingInterface = () => {
    if (!selectedService) return null;

    const service = categoryServices.find(s => s.id === selectedService);
    if (!service) return null;
    const serviceMappings = mappings[selectedService] || [];

    // Group mappings by data model (only top-level fields)
    const mappingsByModel = serviceMappings
      .filter(mapping => !mapping.parentFieldId) // Only include top-level fields
      .reduce((acc, mapping) => {
        const model = mapping.customFieldModel || 'general';
        if (!acc[model]) acc[model] = [];
        acc[model].push(mapping);
        return acc;
      }, {} as Record<string, FieldMapping[]>);

    const toggleModelGroup = (model: string) => {
      setExpandedModelGroups(prev => {
        const isExpanding = !prev.includes(model);
        // Don't fetch immediately - wait for dropdown click
        return prev.includes(model)
          ? prev.filter(m => m !== model)
          : [...prev, model];
      });
    };



    return (
      <>
        <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: 3 }}>
          <Typography variant="h6" fontWeight={600}>
            Map fields for {service?.name}
          </Typography>
          {/* Hidden for initial release */}
          {false && (
            <Stack direction="row" spacing={1}>
              <FormControlLabel
                control={
                  <Switch
                    checked={bulkMode}
                    onChange={(e) => setBulkMode(e.target.checked)}
                    size="small"
                  />
                }
                label="Bulk mode"
                sx={{ mr: 1 }}
              />
              <IconButton
                size="small"
                onClick={(e) => setAnchorEl(e.currentTarget)}
              >
                <MoreVertical size={16} />
              </IconButton>
            </Stack>
          )}
        </Stack>

        <TextField
          fullWidth
          size="small"
          placeholder="Search custom fields..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Search size={16} />
              </InputAdornment>
            )
          }}
          sx={{ mb: 3 }}
        />

        {Object.entries(mappingsByModel)
          .sort((a, b) => {
            // Define a custom sort order
            const order = ['organization', 'repository', 'board', 'pull_request','branch', 'commit', 'general','user','collections','ticket','comment','attachment','label','project','issue'];
            const indexA = order.indexOf(a[0]);
            const indexB = order.indexOf(b[0]);
            
            if (indexA === -1 && indexB === -1) return a[0].localeCompare(b[0]);
            if (indexA === -1) return 1;
            if (indexB === -1) return -1;
            return indexA - indexB;
          })
          .map(([model, modelMappings], index) => {
          const isExpanded = expandedModelGroups.includes(model);
          // Helper function to check if field or any of its children match search
          const matchesSearch = (mapping: FieldMapping, query: string): boolean => {
            const lowerQuery = query.toLowerCase();
            
            // Check current field
            if (mapping.customFieldName.toLowerCase().includes(lowerQuery) ||
                mapping.customFieldKey.toLowerCase().includes(lowerQuery)) {
              return true;
            }
            
            // Check children recursively
            if (mapping.children && mapping.children.length > 0) {
              return mapping.children.some(child => matchesSearch(child, query));
            }
            
            return false;
          };
          
          const filteredMappings = searchQuery 
            ? modelMappings.filter(m => matchesSearch(m, searchQuery))
            : modelMappings;

          if (filteredMappings.length === 0 && searchQuery) return null;

          const { total: totalCount, mapped: mappedCount } = countMappedFields(filteredMappings);
          
          // Get service field groups for this specific model
          const serviceFieldGroups = getServiceFieldGroups(selectedService, model);
          const isLoadingModelSpecs = loadingSpecifications[`${selectedService}-${model}`] || false;

          // Recursive function to render field mapping
          const renderFieldMapping = (mapping: FieldMapping, depth: number = 0, parentPath: string = '') => {
            const hasChildren = mapping.children && mapping.children.length > 0;
            const isObjectType = mapping.customFieldType === 'object' || mapping.customFieldType === 'array';
            const isExpanded = expandedFields.includes(mapping.customFieldId);
            
            // Construct the full field path
            const fieldPath = parentPath ? `${parentPath}.${mapping.customFieldKey}` : mapping.customFieldKey;
            
            return (
              <Box key={mapping.customFieldId}>
                <Paper
                  elevation={0}
                  sx={{
                    p: 2,
                    mb: 1.5,
                    ml: depth * 4,
                    border: `1px solid ${
                      mapping.hasTypeError 
                        ? theme.palette.warning.main
                        : theme.palette.divider
                    }`,
                    borderRadius: 1,
                    backgroundColor: mapping.hasTypeError
                      ? alpha(theme.palette.warning.main, 0.04)
                      : mapping.mapped
                      ? alpha(theme.palette.success.main, 0.04)
                      : theme.palette.background.paper,
                    position: 'relative',
                    // Add visual indicator for nested fields
                    // '&::before': depth > 0 ? {
                    //   content: '""',
                    //   position: 'absolute',
                    //   left: -theme.spacing(2),
                    //   top: '50%',
                    //   width: theme.spacing(2),
                    //   height: 1,
                    //   backgroundColor: theme.palette.divider
                    // } : {}
                  }}
                >
                  <Stack direction="row" alignItems="center" spacing={2}>
                    {/* Expand/collapse button for object types */}
                    {hasChildren && (
                      <IconButton
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation();
                          setExpandedFields(prev =>
                            prev.includes(mapping.customFieldId)
                              ? prev.filter(id => id !== mapping.customFieldId)
                              : [...prev, mapping.customFieldId]
                          );
                        }}
                        sx={{ p: 0.5 }}
                      >
                        {isExpanded ? <ChevronDown size={14} /> : <ChevronRight size={14} />}
                      </IconButton>
                    )}
                    
                    {/* Hidden for initial release */}
                    {false && bulkMode && (
                      <Checkbox
                        checked={selectedFields.includes(mapping.customFieldId)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedFields(prev => [...prev, mapping.customFieldId]);
                          } else {
                            setSelectedFields(prev => prev.filter(id => id !== mapping.customFieldId));
                          }
                        }}
                      />
                    )}
                    
                    <Box sx={{ flex: 1 }}>
                      <Stack direction="row" alignItems="center" spacing={1}>
                        {isObjectType && (
                          <Chip
                            size="small"
                            label={mapping.customFieldType}
                            color="primary"
                            variant="outlined"
                            sx={{ height: 18, fontSize: '0.65rem' }}
                          />
                        )}
                        <Typography variant="body2" fontWeight={600}>
                          {mapping.customFieldName}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          ({fieldPath})
                        </Typography>
                        {!isObjectType && mapping.customFieldType && (
                          <Chip
                            size="small"
                            label={mapping.customFieldType}
                            variant="outlined"
                            sx={{ 
                              height: 18, 
                              fontSize: '0.65rem',
                              borderColor: theme.palette.divider,
                              color: theme.palette.text.secondary
                            }}
                          />
                        )}
                        {mapping.suggested && (
                          <Chip
                            size="small"
                            label="Auto-suggested"
                            color="info"
                            sx={{ height: 20, fontSize: '0.7rem' }}
                          />
                        )}
                      </Stack>
                    </Box>

                    {mapping.hasTypeError && (
                      <Tooltip
                        title={
                          <Stack spacing={0.5}>
                            <Typography variant="caption" sx={{ fontWeight: 600 }}>
                              Type Mismatch Warning
                            </Typography>
                            <Typography variant="caption">
                              Custom field type: {mapping.customFieldType}
                            </Typography>
                            <Typography variant="caption">
                              Service field type: {mapping.serviceFieldType}
                            </Typography>
                            <Typography variant="caption" sx={{ mt: 0.5 }}>
                              This may cause data conversion issues
                            </Typography>
                          </Stack>
                        }
                        placement="top"
                        arrow
                      >
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <AlertCircle 
                            size={16} 
                            color={theme.palette.warning.main}
                          />
                        </Box>
                      </Tooltip>
                    )}

                    <Tooltip 
                      title={
                        mapping.mapped 
                          ? (
                            <Stack spacing={0.5}>
                              <Typography variant="caption" sx={{ fontWeight: 600 }}>
                                Field Mapping
                              </Typography>
                              <Typography variant="caption">
                                {mapping.customFieldName} → {mapping.serviceFieldName}
                              </Typography>
                              <Typography variant="caption" sx={{ opacity: 0.8 }}>
                                {service?.displayName || service?.name} field
                              </Typography>
                            </Stack>
                          )
                          : `Not mapped to any ${service?.displayName || service?.name} field`
                      }
                      placement="top"
                      arrow
                    >
                      <Box sx={{ display: 'flex', alignItems: 'center', cursor: 'help' }}>
                        <Link2 
                          size={16} 
                          color={mapping.hasTypeError ? theme.palette.warning.main : mapping.mapped ? theme.palette.success.main : theme.palette.text.secondary}
                          style={{ opacity: mapping.mapped ? 1 : 0.5 }}
                        />
                      </Box>
                    </Tooltip>

                    <Box sx={{ minWidth: 250 }}>
                      <TextField
                        key={`${renderKey}-${selectedService}-${model}-${mapping.customFieldId}`}
                        fullWidth
                        size="small"
                        placeholder="Select service field..."
                        value={mapping.serviceFieldPath || ''}
                        select
                        error={mapping.hasTypeError}
                        helperText={mapping.hasTypeError ? `Type mismatch: ${mapping.customFieldType} → ${mapping.serviceFieldType}` : ''}
                        label={`${service?.name} field`}
                        InputLabelProps={{
                          shrink: true,
                          sx: { 
                            color: 'text.secondary',
                            fontSize: '0.75rem',
                            fontWeight: 400
                          }
                        }}
                        SelectProps={{
                          onOpen: async () => {
                            // Fetch only the first missing dataType for this model
                            const keys = getModelDataTypeKeys(model);
                            const firstMissing = keys.find(dtKey => {
                              const cacheKey = `${selectedService}-${model}-${dtKey}`;
                              return !modelSpecifications[cacheKey] && !loadingSpecifications[cacheKey];
                            });
                            if (firstMissing) await fetchModelSpecifications(model, firstMissing);
                          },
                          // Ensure the value is always valid
                          renderValue: (selected) => {
                            if (!selected) return <em>None</em>;
                            const field = serviceFieldGroups.flatMap(g => g.fields).find(f => f.path === selected);
                            return field?.name || selected;
                          }
                        }}
                        onChange={(e) => {
                          const value = e.target.value as string;
                          if (!value) {
                            handleFieldMapping(selectedService, mapping.customFieldId, '', '', '');
                            return;
                          }
                          const selectedField = serviceFieldGroups
                            .flatMap(g => g.fields)
                            .find(f => f.path === value);
                          if (selectedField) {
                            const isCompatible = !mapping.customFieldType || 
                              areTypesCompatible(mapping.customFieldType, selectedField.type);
                            if (!isCompatible) {
                              toast.error(`Type mismatch: ${mapping.customFieldType} → ${selectedField.type}. Mapping not allowed.`);
                              return;
                            }
                            handleFieldMapping(
                              selectedService,
                              mapping.customFieldId,
                              selectedField.path,
                              selectedField.name,
                              selectedField.type
                            );
                          }
                        }}
                      >
                        <MenuItem value="">
                          <em>None</em>
                        </MenuItem>
                        {isLoadingModelSpecs ? (
                          <MenuItem disabled>
                            <Stack direction="row" alignItems="center" spacing={1}>
                              <CircularProgress size={16} />
                              <Typography variant="body2">Loading fields...</Typography>
                            </Stack>
                          </MenuItem>
                        ) : serviceFieldGroups.length === 0 ? (
                          <MenuItem disabled>
                            <Typography variant="body2" color="text.secondary">
                              No fields available
                            </Typography>
                          </MenuItem>
                        ) : (
                          serviceFieldGroups.map(group => [
                          <MenuItem key={`group-${group.groupName}`} disabled>
                            <Typography variant="caption" fontWeight={600}>
                              {group.groupName}
                            </Typography>
                          </MenuItem>,
                          ...group.fields.map(field => {
                            const isCompatible = !mapping.customFieldType || 
                              areTypesCompatible(mapping.customFieldType, field.type);
                            
                            return (
                              <MenuItem 
                                key={field.path} 
                                value={field.path}
                                disabled={!isCompatible}
                                sx={{
                                  pl: field.isNested ? 2 + (field.level || 0) * 2 : 2,
                                  py: 1
                                }}
                              >
                                <Stack spacing={0.5} sx={{ width: '100%' }}>
                                  <Stack direction="row" alignItems="center" spacing={1}>
                                    <Typography 
                                      variant="body2" 
                                      sx={{ 
                                        flex: 1,
                                        color: isCompatible ? 'inherit' : 'text.secondary',
                                        fontWeight: field.type === 'object' ? 500 : 400
                                      }}
                                    >
                                      {field.name}
                                    </Typography>
                                    {!isCompatible && (
                                      <Tooltip title="Type mismatch - may cause issues">
                                        <AlertCircle size={14} color={theme.palette.warning.main} />
                                      </Tooltip>
                                    )}
                                    <Typography 
                                      variant="caption" 
                                      color={isCompatible ? 'text.secondary' : 'warning.main'}
                                      sx={{ 
                                        fontFamily: 'monospace',
                                        fontSize: '0.7rem'
                                      }}
                                    >
                                      {field.type}
                                    </Typography>
                                  </Stack>
                                  {field.description && (
                                    <Typography 
                                      variant="caption" 
                                      color="text.secondary"
                                      sx={{ 
                                        pl: field.isNested ? 0 : 0,
                                        fontSize: '0.68rem',
                                        lineHeight: 1.3
                                      }}
                                    >
                                      {field.description}
                                    </Typography>
                                  )}
                                </Stack>
                              </MenuItem>
                            );
                          })
                        ])
                        )}
                      </TextField>
                    </Box>

                    {mapping.mapped && (
                      <IconButton
                        size="small"
                        onClick={() => handleFieldMapping(selectedService, mapping.customFieldId, '', '', '')}
                        sx={{ ml: 1 }}
                      >
                        <X size={16} />
                      </IconButton>
                    )}
                  </Stack>
                </Paper>
                
                {/* Render child fields if any */}
                {hasChildren && mapping.children && (
                  <Collapse in={isExpanded}>
                    <Box sx={{ mt: 1 }}>
                      {mapping.children.map(childMapping => 
                        renderFieldMapping(childMapping, depth + 1, fieldPath)
                      )}
                    </Box>
                  </Collapse>
                )}
              </Box>
            );
          };

          return (
            <Box key={`model-group-${selectedService}-${model}-${index}`} sx={{ mb: 3 }}>
              <Box sx={{ mb: 1.5 }}>
                <Stack 
                  direction="row" 
                  alignItems="center" 
                  justifyContent="space-between"
                  sx={{ width: '100%' }}
                >
                  {/* Left section - clickable */}
                  <Stack 
                    direction="row" 
                    alignItems="center" 
                    spacing={1}
                    onClick={() => toggleModelGroup(model)}
                    sx={{ 
                      cursor: 'pointer',
                      flex: 1,
                      '&:hover': { opacity: 0.8 }
                    }}
                  >
                    <IconButton size="small" sx={{ p: 0.5 }}>
                      {isExpanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
                    </IconButton>
                    
                    {/* Unizo Model */}
                    <Stack direction="row" alignItems="center" spacing={1}>
                      <Typography variant="body1" fontWeight={600}>
                        {modelConfig[model]?.name || model}
                      </Typography>
                      
                      {/* Mapping Indicator */}
                      {selectedServiceProfile && (
                        <Tooltip
                          title={(() => {
                            const connectorModel = getConnectorModelInfo(model);
                            return connectorModel 
                              ? `Unizo ${modelConfig[model]?.name} maps to ${selectedServiceProfile.service?.name} ${connectorModel.name}`
                              : `Maps to ${selectedServiceProfile.service?.name}'s ${model} model`;
                          })()}
                          placement="top"
                        >
                          <Box sx={{ 
                            display: 'flex', 
                            alignItems: 'center',
                            cursor: 'help'
                          }}>
                            <Zap 
                              size={14} 
                              color={theme.palette.primary.main}
                              style={{ 
                                transform: 'rotate(90deg)',
                                opacity: 0.7 
                              }}
                            />
                          </Box>
                        </Tooltip>
                      )}
                      
                      {/* Connector-specific Model */}
                      {selectedServiceProfile && (
                        (() => {
                          const connectorModels = getConnectorModelInfo(model);
                          
                          if (!connectorModels && !availableDataTypes.length) {
                            // Loading state
                            return (
                              <Skeleton 
                                variant="rectangular" 
                                width={120} 
                                height={22} 
                                sx={{ borderRadius: 1 }} 
                              />
                            );
                          }
                          
                          if (!connectorModels) {
                            return null;
                          }
                          
                          // Show multiple dataTypes as chips
                          return (
                            <Stack direction="row" spacing={0.5}>
                              {connectorModels.map((connectorModel: any) => (
                                <Tooltip
                                  key={connectorModel.key}
                                  title={`${selectedServiceProfile.service?.name}: ${connectorModel.description || `${connectorModel.name} entity`}`}
                                  placement="top"
                                >
                                  <Chip
                                    size="small"
                                    label={`${selectedServiceProfile.service?.name}: ${connectorModel.name}`}
                                    icon={
                                      selectedServiceProfile.service?.imageUrl ? (
                                        <img 
                                          src={selectedServiceProfile.service?.imageUrl} 
                                          alt={selectedServiceProfile.service?.name}
                                          style={{ 
                                            width: 16, 
                                            height: 16, 
                                            objectFit: 'contain',
                                            marginLeft: 4
                                          }}
                                        />
                                      ) : undefined
                                    }
                                    sx={{
                                      height: 24,
                                      '& .MuiChip-label': {
                                        px: 1,
                                        fontSize: '0.7rem',
                                        fontWeight: 500
                                      },
                                      backgroundColor: alpha(theme.palette.primary.main, 0.08),
                                      border: 'none',
                                      color: theme.palette.primary.main
                                    }}
                                  />
                                </Tooltip>
                              ))}
                            </Stack>
                          );
                        })()
                      )}
                    </Stack>
                  </Stack>
                  
                   {/* Right section - chips */}
                   {(() => {
                     const connectorModels = getConnectorModelInfo(model);
                     const isUnsupported = !connectorModels && availableDataTypes.length > 0;
                     const hasNoDataTypes = availableDataTypes.length === 0;
                     
                     // Don't show mapped/unmapped counts for unsupported models or when no data types
                     if (isUnsupported || hasNoDataTypes) {
                       return null;
                     }
                     
                     return (
                       <Stack direction="row" spacing={1}>
                         {mappedCount > 0 && (
                           <Chip 
                             size="small" 
                             label={`${mappedCount} mapped`}
                             color="success"
                             variant="outlined"
                             sx={{ height: 20, fontSize: '0.7rem' }}
                           />
                         )}
                         {totalCount - mappedCount > 0 && (
                           <Chip 
                             size="small" 
                             label={`${totalCount - mappedCount} unmapped`}
                             color="default"
                             variant="outlined"
                             sx={{ height: 20, fontSize: '0.7rem' }}
                           />
                         )}
                       </Stack>
                     );
                   })()}
                </Stack>
              </Box>

              <Collapse in={isExpanded}>
                <Box sx={{ pl: 5, pt: 2 }}>
                  {/* Mapping explanation */}
                  {(() => {
                    const connectorModels = getConnectorModelInfo(model);
                    if (!selectedServiceProfile) return null;

                    // Unsupported model for this connector: show warning and block mapping UI
                    if (!connectorModels && availableDataTypes.length > 0) {
                      return (
                        <Box
                          sx={{ 
                            mb: 2,
                            p: 1,
                            borderRadius: 1,
                            backgroundColor: alpha(theme.palette.warning.main, 0.08),
                            border: `1px solid ${alpha(theme.palette.warning.main, 0.2)}`
                          }}
                        >
                          <Typography variant="caption" color="text.secondary">
                            {selectedServiceProfile.service?.name} doesn't support <strong>{model}</strong> model.
                          </Typography>
                        </Box>
                      );
                    }

                    // Supported: show info tip
                    if (connectorModels && connectorModels.length > 0) {
                      return (
                        <Box
                          sx={{ 
                            mb: 2,
                            p: 1,
                            borderRadius: 1,
                            backgroundColor: alpha(theme.palette.info.main, 0.08),
                            border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`
                          }}
                        >
                          <Typography variant="caption" color="text.secondary">
                            You're mapping Unizo's unified <strong>{modelConfig[model]?.name}</strong> fields to {selectedServiceProfile.service?.name}'s {connectorModels.length > 1 ? 'multiple' : ''} <strong>{connectorModels.map(m => m.name).join(', ')}</strong> {connectorModels.length > 1 ? 'entities' : 'entity'}.
                          </Typography>
                        </Box>
                      );
                    }

                    return null;
                  })()}

                   {(() => {
                     const connectorModels = getConnectorModelInfo(model);
                     const isUnsupported = !connectorModels && availableDataTypes.length > 0;
                     const isLoadingModel = availableDataTypes.some((dataType: any) => 
                       loadingSpecifications[`${selectedService}-${model}-${dataType.key}`]
                     );
                     
                     if (isUnsupported) {
                       // Do not render mapping controls when connector doesn't support this model
                       return null;
                     }
                     
                     // If we have no data types at all (empty API response), show empty state
                     if (availableDataTypes.length === 0) {
                       return (
                         <Box
                           sx={{ 
                             mb: 2,
                             p: 2,
                             borderRadius: 1,
                             backgroundColor: alpha(theme.palette.info.main, 0.08),
                             border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`,
                             textAlign: 'center'
                           }}
                         >
                           <Typography variant="body2" color="text.secondary">
                             No data types available for {selectedServiceProfile?.service?.name}
                           </Typography>
                         </Box>
                       );
                     }
                     
                     // If we have data types but no connector models for this specific model, show empty state
                     if (availableDataTypes.length > 0 && !connectorModels) {
                       return (
                         <Box
                           sx={{ 
                             mb: 2,
                             p: 2,
                             borderRadius: 1,
                             backgroundColor: alpha(theme.palette.info.main, 0.08),
                             border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`,
                             textAlign: 'center'
                           }}
                         >
                           <Typography variant="body2" color="text.secondary">
                             No {modelConfig[model]?.name || model} fields available for {selectedServiceProfile?.service?.name}
                           </Typography>
                         </Box>
                       );
                     }
                     
                     if (isLoadingModel && filteredMappings.length > 0) {
                       // Show skeleton loaders while loading
                       return (
                         <Stack spacing={1.5}>
                           {[...Array(Math.min(3, filteredMappings.length))].map((_, idx) => (
                             <Skeleton 
                               key={idx} 
                               variant="rectangular" 
                               height={80} 
                               sx={{ borderRadius: 1 }} 
                             />
                           ))}
                         </Stack>
                       );
                     }
                     
                     return (
                       <Stack spacing={1.5}>
                         {filteredMappings.map(mapping => renderFieldMapping(mapping))}
                       </Stack>
                     );
                   })()}
                </Box>
              </Collapse>
            </Box>
          );
        })}

        {/* Hidden for initial release */}
        {false && bulkMode && selectedFields.length > 0 && (
          <Paper
            elevation={0}
            sx={{
              position: 'sticky',
              bottom: 0,
              p: 2,
              mt: 2,
              backgroundColor: theme.palette.background.paper,
              borderTop: `1px solid ${theme.palette.divider}`,
              borderRadius: 1,
              boxShadow: theme.shadows[4]
            }}
          >
            <Stack direction="row" alignItems="center" justifyContent="space-between">
              <Typography variant="body2">
                {selectedFields.length} fields selected
              </Typography>
              <Stack direction="row" spacing={1}>
                <Button
                  size="small"
                  variant="outlined"
                  onClick={() => handleBulkAction('unmap')}
                >
                  Unmap Selected
                </Button>
                <Button
                  size="small"
                  variant="contained"
                  onClick={() => setSelectedFields([])}
                >
                  Clear Selection
                </Button>
              </Stack>
            </Stack>
          </Paper>
        )}
      </>
    );
  };

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column', overflow: 'hidden' }}>
      {!isOnline && (
        <Alert 
          severity="warning" 
          icon={<AlertCircle size={20} />}
          sx={{ 
            borderRadius: 0,
            borderBottom: `1px solid ${theme.palette.divider}`
          }}
        >
          <Typography variant="body2">
            You are currently offline. Changes made will not be saved until connection is restored.
          </Typography>
        </Alert>
      )}
      
      <Box sx={{ px: 3, pt: 2, pb: 1 }}>
        <Stack direction="row" alignItems="flex-start" justifyContent="space-between">
          <Box sx={{ flex: 1 }}>
            <Typography variant="h6" fontWeight={600} gutterBottom>
              Map your custom fields to connector-specific fields
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Connect your custom fields to each connector's API fields. We've pre-populated suggested mappings based on field names.
            </Typography>
          </Box>
          <Button
            variant="outlined"
            size="small"
            onClick={() => {
              // Apply all suggestions across all services
              const applySuggestionsRecursively = (mappings: FieldMapping[]): FieldMapping[] => {
                return mappings.map(mapping => {
                  const updated = mapping.suggested ? { ...mapping, mapped: true } : mapping;
                  
                  if (updated.children && updated.children.length > 0) {
                    return {
                      ...updated,
                      children: applySuggestionsRecursively(updated.children)
                    };
                  }
                  
                  return updated;
                });
              };
              
              const updatedMappings = { ...mappings };
              Object.keys(updatedMappings).forEach(serviceId => {
                updatedMappings[serviceId] = applySuggestionsRecursively(updatedMappings[serviceId]);
              });
              setMappings(updatedMappings);
              toast.success('Applied all suggested mappings');
            }}
            startIcon={<Zap size={16} />}
            sx={{ ml: 2, whiteSpace: 'nowrap' }}
          >
            Apply All Suggestions
          </Button>
        </Stack>
      </Box>

      <Box sx={{ flex: 1, px: 3, overflow: 'hidden' }}>
        {/* Error State */}
        {(customFieldsError || servicesError) && !isLoadingCustomFields && !isLoadingServices ? (
          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', height: '100%' }}>
            <AlertCircle size={48} color={theme.palette.error.main} />
            <Typography variant="h6" sx={{ mt: 2, mb: 1 }}>
              Failed to Load Data
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3, textAlign: 'center', maxWidth: 400 }}>
              {customFieldsError ? 'Unable to load custom fields.' : 'Unable to load services.'}
              &nbsp; Please check your connection and try again.
            </Typography>
            <Stack direction="row" spacing={2}>
              <Button 
                variant="outlined" 
                onClick={() => customFieldsError ? refetchCustomFields() : refetchServices()}
                startIcon={<Loader2 size={16} />}
              >
                Retry
              </Button>
              <Button 
                variant="text" 
                onClick={onBack}
              >
                Go Back
              </Button>
            </Stack>
          </Box>
        ) : (isLoadingCustomFields || isLoadingServices) ? (
          <Grid container spacing={2} sx={{ height: '100%' }}>
            {/* Loading skeleton for service list */}
            <Grid item xs={12} md={4} sx={{ height: '100%' }}>
              <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                <Typography variant="body2" fontWeight={600} sx={{ mb: 2 }}>
                  Select a connector to configure
                </Typography>
                <Box sx={{ flex: 1, overflow: 'auto', pr: 1 }}>
                  <Stack spacing={1.5}>
                    {[1, 2, 3].map(i => (
                      <Paper
                        key={i}
                        elevation={0}
                        sx={{
                          p: 1.5,
                          border: `1.5px solid ${theme.palette.divider}`,
                          borderRadius: 1,
                        }}
                      >
                        <Stack direction="row" alignItems="center" spacing={1.5}>
                          <Skeleton variant="rectangular" width={32} height={32} sx={{ borderRadius: 0.5 }} />
                          <Box sx={{ flex: 1 }}>
                            <Skeleton variant="text" width="60%" />
                            <Skeleton variant="text" width="40%" height={16} />
                          </Box>
                        </Stack>
                      </Paper>
                    ))}
                  </Stack>
                </Box>
              </Box>
            </Grid>
            {/* Loading skeleton for mapping area */}
            <Grid item xs={12} md={8} sx={{ height: '100%' }}>
              <Box sx={{ 
                height: '100%', 
                backgroundColor: theme.palette.mode === 'dark' 
                  ? alpha(theme.palette.background.paper, 0.6) 
                  : theme.palette.grey[50],
                borderRadius: 1,
                p: 2,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <Typography variant="body2" color="text.secondary">
                  Select a service to start mapping fields
                </Typography>
              </Box>
            </Grid>
          </Grid>
        ) : customFieldsData?.data?.data?.filter(f => f.category?.type === category).length === 0 ? (
          <Alert severity="info" sx={{ mt: 2 }}>
            <Typography variant="body2">
              No custom fields found. Please go back and add custom fields first.
            </Typography>
          </Alert>
        ) : (
          <Grid container spacing={2} sx={{ height: '100%' }}>
            <Grid item xs={12} md={4} sx={{ height: '100%' }}>
              <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                <Typography variant="body2" fontWeight={600} sx={{ mb: 2 }}>
                  Select a connector to configure
                </Typography>
                <Box sx={{ flex: 1, overflow: 'auto', pr: 1 }}>
                  <Stack spacing={1.5}>
                    {categoryServices.length === 0 ? (
                      <Alert severity="info">
                        <Typography variant="body2">
                          No active {categoryLabel} connectors found. Please enable connectors in the Connectors Page first.
                        </Typography>
                      </Alert>
                    ) : (
                      categoryServices.map(renderServiceCard)
                    )}
                  </Stack>
                </Box>
              </Box>
            </Grid>
            
            <Grid item xs={12} md={8} sx={{ height: '100%' }}>
              <Box 
                ref={mappingContentRef}
                sx={{ 
                  height: '100%', 
                  overflow: 'auto',
                  backgroundColor: theme.palette.mode === 'dark' 
                    ? alpha(theme.palette.background.paper, 0.6) 
                    : theme.palette.grey[50],
                  borderRadius: 1,
                  p: 2
                }}>
                {selectedService ? renderMappingInterface() : (
                  <Box sx={{ 
                    display: 'flex', 
                    alignItems: 'center', 
                    justifyContent: 'center',
                    height: '100%',
                    color: theme.palette.text.secondary
                  }}>
                    <Stack alignItems="center" spacing={2}>
                      <Link size={48} strokeWidth={1} />
                      <Typography variant="body2">
                        Select a service to start mapping fields
                      </Typography>
                    </Stack>
                  </Box>
                )}
              </Box>
            </Grid>
          </Grid>
        )}
      </Box>

      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={() => setAnchorEl(null)}
      >
        <MenuItem onClick={() => {
          // Apply suggested mappings
          if (selectedService) {
            const applySuggestionsRecursively = (mappings: FieldMapping[]): FieldMapping[] => {
              return mappings.map(mapping => {
                const updated = mapping.suggested ? { ...mapping, mapped: true } : mapping;
                
                if (updated.children && updated.children.length > 0) {
                  return {
                    ...updated,
                    children: applySuggestionsRecursively(updated.children)
                  };
                }
                
                return updated;
              });
            };
            
            setMappings(prev => ({
              ...prev,
              [selectedService]: applySuggestionsRecursively(prev[selectedService])
            }));
          }
          setAnchorEl(null);
        }}>
          <Stack direction="row" spacing={1}>
            <Zap size={16} />
            <Typography variant="body2">Apply all suggestions</Typography>
          </Stack>
        </MenuItem>
        <MenuItem onClick={() => {
          // Clear all mappings
          if (selectedService) {
            const clearMappingsRecursively = (mappings: FieldMapping[]): FieldMapping[] => {
              return mappings.map(mapping => {
                const cleared = { 
                  ...mapping, 
                  serviceFieldPath: '', 
                  serviceFieldName: '', 
                  mapped: false,
                  suggested: false 
                };
                
                if (cleared.children && cleared.children.length > 0) {
                  return {
                    ...cleared,
                    children: clearMappingsRecursively(cleared.children)
                  };
                }
                
                return cleared;
              });
            };
            
            setMappings(prev => ({
              ...prev,
              [selectedService]: clearMappingsRecursively(prev[selectedService])
            }));
          }
          setAnchorEl(null);
        }}>
          <Stack direction="row" spacing={1}>
            <X size={16} />
            <Typography variant="body2">Clear all mappings</Typography>
          </Stack>
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default ServiceFieldMappingStep;