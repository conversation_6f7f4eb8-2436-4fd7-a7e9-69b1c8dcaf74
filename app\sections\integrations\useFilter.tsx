import { useMemo } from "react";
import { ColumnFiltersState } from '@tanstack/react-table';
import { useGetServiceProfile } from "hooks/api/service-profile/useGetServiceProfile";
import { useStatus, State } from "hooks/useStatus";
import useUserDetails from "store/user";

type UseIntegrationFilterProps = {
   selectedProvider: string
   columnFilters: ColumnFiltersState
}

export default function useIntegrationFilter(
   {
      selectedProvider,
      columnFilters
   }: UseIntegrationFilterProps
) {

   const { subscriptions } = useUserDetails();
   const { resolveStatus } = useStatus();

   const categoriesOptions = useMemo(() => {
      return subscriptions?.map((item) => (
         { label: item.name, value: item?.productKey }
      )) ?? []
   }, [subscriptions]);

   // Process the selectedProvider to handle array values and convert labels to values
   const processedType = useMemo(() => {
      if (!selectedProvider) return undefined;


      // If it's an array, extract the first value and convert label to value
      if (Array.isArray(selectedProvider)) {
         const firstValue = selectedProvider.length > 0 ? selectedProvider[0] : undefined;
         if (firstValue && categoriesOptions.length > 0) {
            const matched = categoriesOptions.find(opt => opt.label === firstValue);
            const result = matched?.value || firstValue;
            return result;
         }
         return firstValue;
      }

      // If it's a string, convert label to value
      if (categoriesOptions.length > 0) {
         const matched = categoriesOptions.find(opt => opt.label === selectedProvider);
         const result = matched?.value || selectedProvider;
         return result;
      }

      return selectedProvider;
   }, [selectedProvider, categoriesOptions]);

   // Always get ALL providers by passing a special value that won't filter
   const { serviceProfileClient } = useGetServiceProfile({
      searchOptions: {
         search: {
            limit: 200, // Increase limit to get more providers
            offset: 0,
            type: "GET_ALL_PROVIDERS" // Special value that we'll handle in the helper
         }
      }
   });

   const providerOptions = useMemo(() => {
      const allProviders = serviceProfileClient?.data || [];

      // If no category is selected, show all providers
      if (!processedType) {
         console.log('useIntegrationFilter - Showing ALL providers:', allProviders.length);
         return allProviders.map((item: any) => ({
            label: item.name,
            value: item?.id
         }));
      }

      // If category is selected, filter providers by type
      const filteredProviders = allProviders.filter((item: any) => item.type === processedType);

      return filteredProviders.map((item: any) => ({
         label: item.name,
         value: item?.id
      }));
   }, [serviceProfileClient?.data, processedType]);

   const stateOptions = useMemo(() => [
      { label: resolveStatus('ACTIVE' as State), value: 'ACTIVE' },
      { label: resolveStatus('INACTIVE' as State), value: 'INACTIVE' },
      { label: resolveStatus('VERIFYING' as State), value: 'VERIFYING' },
      { label: resolveStatus('FAILED' as State), value: 'FAILED' },
   ], [resolveStatus]);


   return {
      providerOptions,
      categoriesOptions,
      stateOptions,
   }
} 