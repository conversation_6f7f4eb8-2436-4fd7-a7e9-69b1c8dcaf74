import React, { createContext, useContext, useEffect, useCallback, ReactNode } from 'react';
import { useLocation } from '@remix-run/react';
import { 
  initializeHotjar, 
  trackHotjarEvent, 
  identifyHotjarUser, 
  trackPageView,
  trackFunnelStep,
  trackUserFeedback,
  HOTJAR_CONFIG
} from 'utils/hotjar';
import { initializeHotjarWithDebug, identifyHotjarUserWithDebug, HOTJAR_DEBUG } from 'utils/hotjar-debug';
import useUserDetails from 'store/user';

interface HotjarContextType {
  isEnabled: boolean;
  trackEvent: (eventName: string, attributes?: Record<string, any>) => void;
  trackPageView: (pageName: string, pageUrl?: string) => void;
  trackFunnelStep: (funnelName: string, stepName: string, stepNumber: number, metadata?: Record<string, any>) => void;
  trackUserFeedback: (feedbackType: string, rating?: number, comment?: string) => void;
  trackUserAction: (action: string, category: string, label?: string, value?: number) => void;
  trackError: (error: Error, context?: Record<string, any>) => void;
  trackFormSubmission: (formName: string, success: boolean, metadata?: Record<string, any>) => void;
  trackSearch: (searchTerm: string, resultsCount: number, filters?: Record<string, any>) => void;
  trackEngagement: (feature: string, action: string, duration?: number) => void;
}

const HotjarContext = createContext<HotjarContextType | undefined>(undefined);

interface HotjarProviderProps {
  children: ReactNode;
}

export const HotjarProviderEnhanced: React.FC<HotjarProviderProps> = ({ children }) => {
  const location = useLocation();
  const { user } = useUserDetails();

  // Initialize Hotjar on mount
  useEffect(() => {
    if (HOTJAR_DEBUG) {
      initializeHotjarWithDebug();
    } else {
      initializeHotjar();
    }
    
    // Log initialization status
    if (HOTJAR_DEBUG) {
      console.log('[HotjarProvider] Initialized with debug mode');
    }
  }, []);

  // Identify user when user data is available
  useEffect(() => {
    const shouldIdentify = HOTJAR_CONFIG.enabled || (HOTJAR_DEBUG && window.location.search.includes('hotjar=true'));
    
    if (shouldIdentify && user?.id) {
      const userAttributes = {
        email: user.email,
        name: user.name || `${user.firstName || ''} ${user.lastName || ''}`.trim() || 'Unknown User',
        organization_id: user.organization?.id || 'no-org',
        organization_name: user.organization?.name || 'No Organization',
        role: user.role || 'unknown',
        created_at: user.createdAt || new Date().toISOString(),
        subscription_tier: user.subscriptionTier || 'free',
        is_trial: user.isTrial !== undefined ? user.isTrial : false,
        environment: process.env.NODE_ENV
      };

      if (HOTJAR_DEBUG) {
        console.log('[HotjarProvider] Identifying user with attributes:', {
          userId: user.id,
          attributes: userAttributes
        });
        identifyHotjarUserWithDebug(user.id, userAttributes);
      } else {
        identifyHotjarUser(user.id, userAttributes);
      }
    } else if (shouldIdentify && !user?.id) {
      // Track anonymous session with a session ID
      const sessionId = `anon_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      if (HOTJAR_DEBUG) {
        console.log('[HotjarProvider] No user ID available, tracking anonymous session:', sessionId);
        identifyHotjarUserWithDebug(sessionId, {
          is_anonymous: true,
          session_start: new Date().toISOString(),
          environment: process.env.NODE_ENV
        });
      } else {
        identifyHotjarUser(sessionId, {
          is_anonymous: true,
          session_start: new Date().toISOString(),
          environment: process.env.NODE_ENV
        });
      }
    }
  }, [user]);

  // Track page views on route change
  useEffect(() => {
    const shouldTrack = HOTJAR_CONFIG.enabled || (HOTJAR_DEBUG && window.location.search.includes('hotjar=true'));
    
    if (shouldTrack) {
      const pageName = getPageNameFromPath(location.pathname);
      trackPageView(pageName, location.pathname + location.search);
      
      if (HOTJAR_DEBUG) {
        console.log('[HotjarProvider] Page view tracked:', {
          pageName,
          url: location.pathname + location.search
        });
      }
    }
  }, [location]);

  // Helper function to get page name from path
  const getPageNameFromPath = (path: string): string => {
    const segments = path.split('/').filter(Boolean);
    
    // Map common paths to readable names
    const pageMap: Record<string, string> = {
      'dashboard': 'Dashboard',
      'quick-start': 'Quick Start',
      'quick-start-v2': 'Quick Start V2',
      'integrations': 'Integrations',
      'logs': 'API Logs',
      'setup-integrations': 'Setup Integrations',
      'profile': 'User Profile',
      'settings': 'Settings',
      'webhooks': 'Webhooks',
      'api-keys': 'API Keys',
      'environments': 'Environments',
      'billing': 'Billing',
      'support': 'Support',
      'field-mappings': 'Field Mappings',
      'custom-fields': 'Custom Fields'
    };

    // Get the main page segment (usually the first or second segment)
    const mainSegment = segments[1] || segments[0] || 'home';
    return pageMap[mainSegment] || mainSegment.charAt(0).toUpperCase() + mainSegment.slice(1);
  };

  // Track user actions with category classification
  const trackUserAction = useCallback((
    action: string, 
    category: string, 
    label?: string, 
    value?: number
  ) => {
    trackHotjarEvent(`user_action_${category}`, {
      action,
      category,
      label,
      value,
      timestamp: new Date().toISOString(),
      page_url: location.pathname,
      user_id: user?.id || 'anonymous'
    });
    
    if (HOTJAR_DEBUG) {
      console.log('[HotjarProvider] User action tracked:', { action, category, label, value });
    }
  }, [location.pathname, user?.id]);

  // Track errors for debugging
  const trackError = useCallback((error: Error, context?: Record<string, any>) => {
    trackHotjarEvent('error_occurred', {
      error_message: error.message,
      error_stack: error.stack,
      error_name: error.name,
      page_url: location.pathname,
      user_agent: navigator.userAgent,
      user_id: user?.id || 'anonymous',
      ...context
    });
    
    if (HOTJAR_DEBUG) {
      console.error('[HotjarProvider] Error tracked:', error, context);
    }
  }, [location.pathname, user?.id]);

  // Track form submissions
  const trackFormSubmission = useCallback((
    formName: string, 
    success: boolean, 
    metadata?: Record<string, any>
  ) => {
    trackHotjarEvent('form_submission', {
      form_name: formName,
      success,
      page_url: location.pathname,
      timestamp: new Date().toISOString(),
      user_id: user?.id || 'anonymous',
      ...metadata
    });
    
    if (HOTJAR_DEBUG) {
      console.log('[HotjarProvider] Form submission tracked:', { formName, success, metadata });
    }
  }, [location.pathname, user?.id]);

  // Track search interactions
  const trackSearch = useCallback((
    searchTerm: string, 
    resultsCount: number, 
    filters?: Record<string, any>
  ) => {
    trackHotjarEvent('search_performed', {
      search_term: searchTerm,
      results_count: resultsCount,
      has_filters: !!filters && Object.keys(filters).length > 0,
      filters,
      page_url: location.pathname,
      timestamp: new Date().toISOString(),
      user_id: user?.id || 'anonymous'
    });
    
    if (HOTJAR_DEBUG) {
      console.log('[HotjarProvider] Search tracked:', { searchTerm, resultsCount, filters });
    }
  }, [location.pathname, user?.id]);

  // Track feature engagement
  const trackEngagement = useCallback((
    feature: string, 
    action: string, 
    duration?: number
  ) => {
    trackHotjarEvent('feature_engagement', {
      feature,
      action,
      duration_seconds: duration,
      page_url: location.pathname,
      timestamp: new Date().toISOString(),
      user_id: user?.id || 'anonymous'
    });
    
    if (HOTJAR_DEBUG) {
      console.log('[HotjarProvider] Engagement tracked:', { feature, action, duration });
    }
  }, [location.pathname, user?.id]);

  const value: HotjarContextType = {
    isEnabled: HOTJAR_CONFIG.enabled,
    trackEvent: trackHotjarEvent,
    trackPageView,
    trackFunnelStep,
    trackUserFeedback,
    trackUserAction,
    trackError,
    trackFormSubmission,
    trackSearch,
    trackEngagement
  };

  return (
    <HotjarContext.Provider value={value}>
      {children}
    </HotjarContext.Provider>
  );
};

// Custom hook to use Hotjar context
export const useHotjar = (): HotjarContextType => {
  const context = useContext(HotjarContext);
  if (!context) {
    throw new Error('useHotjar must be used within HotjarProvider');
  }
  return context;
};

// Convenience hooks (same as original)
export const useTrackClick = () => {
  const { trackUserAction } = useHotjar();
  
  return useCallback((elementName: string, metadata?: Record<string, any>) => {
    trackUserAction('click', 'interaction', elementName, undefined);
    if (metadata) {
      trackHotjarEvent('element_click', { element: elementName, ...metadata });
    }
  }, [trackUserAction]);
};

export const useTrackView = () => {
  const { trackUserAction } = useHotjar();
  
  return useCallback((viewName: string, duration?: number) => {
    trackUserAction('view', 'engagement', viewName, duration);
  }, [trackUserAction]);
};

export const useTrackConversion = () => {
  const { trackFunnelStep } = useHotjar();
  
  return useCallback((conversionType: string, value?: number, metadata?: Record<string, any>) => {
    trackHotjarEvent('conversion', {
      type: conversionType,
      value,
      currency: 'USD',
      timestamp: new Date().toISOString(),
      ...metadata
    });
  }, []);
};