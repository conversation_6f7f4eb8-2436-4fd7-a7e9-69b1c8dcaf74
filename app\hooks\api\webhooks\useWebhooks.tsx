import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { webhookClient } from 'services/webhook.service';
import { CreateWebhookPayload, UpdateWebhookPayload } from 'types/webhook';
import useUserDetails from 'store/user';
import { API_ENDPOINTS } from 'utils/api/api-endpoints';

export const useWebhooks = () => {
    const { user } = useUserDetails();
    const organizationId = user?.organization?.id;
  const queryClient = useQueryClient();

  const useGetWebhooks = (organizationId: string) => {
    return useQuery({
      queryKey: ['webhooks', organizationId],
      queryFn: () => webhookClient.getWebhooks(),
      enabled: !!organizationId,
      staleTime: 5 * 60 * 1000 // 5 minutes
    });
  };

  const { mutateAsync: createWebhook } = useMutation({
    mutationFn: ({ payload }: { payload: Record<string, any> }) => {
        return webhookClient.createWebhook(payload);
      },
  });

  const { mutateAsync: updateWebhook } = useMutation({
    mutationFn: ({ payload , id}: { payload: Record<string, any> , id: string }) => {
        return webhookClient.updateWebhook(id as string, payload);
      },
  });
  const { mutateAsync: searchWebhooks,
    isPending: isSearching,
    error: searchError,
    data: searchResult} = useMutation({
    mutationFn: ({ payload }: { payload: Record<string, any> }) => {
      return webhookClient.searchWebhooks(payload);
    },
  });
  const { mutateAsync: validateWebhookEndpoint } = useMutation({
    mutationFn: (url: string) => webhookClient.validateWebhookEndpoint(url),
  });
  const { mutateAsync: deleteWebhook } = useMutation({
    mutationFn: (id: string) => {
      return webhookClient.deleteWebhook(id);
    },
  });

  return {
    attemptCreateWebhook: (payload: Record<string, any>, cb?: any) => {
      toast.promise(createWebhook({ payload }), {
        loading: 'Creating...',
        success: () => {
          // Invalidate legacy list key
          queryClient.invalidateQueries({ queryKey: ['webhooks', organizationId] });
          // Invalidate all search queries keyed by API_ENDPOINTS.WEBHOOKS
          queryClient.invalidateQueries({
            predicate: (q) => Array.isArray(q.queryKey) && q.queryKey[0] === API_ENDPOINTS.WEBHOOKS
          });
          cb && cb();
          return 'Webhook created successfully';
        },
        error: (error: any) => {
          return error.response?.data?.message || 'Failed to create webhook';
        }
      });
    },
    attemptUpdateWebhook: (payload: Record<string, any>, id: string, cb?: any) => {
      toast.promise(updateWebhook({ payload, id }), {
        loading: 'Updating...',
        success: () => {
          // Invalidate legacy list key
          queryClient.invalidateQueries({ queryKey: ['webhooks', organizationId] });  
          // Invalidate all search queries keyed by API_ENDPOINTS.WEBHOOKS
          queryClient.invalidateQueries({
            predicate: (q) => Array.isArray(q.queryKey) && q.queryKey[0] === API_ENDPOINTS.WEBHOOKS
          });
          cb && cb();
          return 'Webhook updated successfully';
        },
        error: (error: any) => {
          return error.response?.data?.message || 'Failed to update webhook';
        }
      });
    },
    attemptSearchWebhooks: async (payload: Record<string, any>) => {
      try {
        const res = await searchWebhooks({ payload });
        return res;
      } catch (err: any) {
        toast.error(
          err?.response?.data?.message || "Search failed"
        );
        throw err;
      }
    },
    attemptValidateWebhookEndpoint: async (url: string) => {
      try {
        const res = await validateWebhookEndpoint(url);
        return res;
      } catch (err: any) {
        toast.error(err?.response?.data?.message || 'Endpoint validation failed');
        throw err;
      }
    },
    attemptDeleteWebhook: (id: string, cb?: any) => {
      toast.promise(deleteWebhook(id), {
        loading: 'Deleting...',
        success: () => {
          // Invalidate legacy list key
          queryClient.invalidateQueries({ queryKey: ['webhooks', organizationId] });  
          // Invalidate all search queries keyed by API_ENDPOINTS.WEBHOOKS
          queryClient.invalidateQueries({
            predicate: (q) => Array.isArray(q.queryKey) && q.queryKey[0] === API_ENDPOINTS.WEBHOOKS
          });
          cb && cb();
          return 'Webhook deleted successfully';
        },
        error: (error: any) => {
          return error.response?.data?.message || 'Failed to delete webhook';
        }
      });
    },

    useGetWebhooks,
    createWebhook,
    updateWebhook,
    deleteWebhook
  };
};